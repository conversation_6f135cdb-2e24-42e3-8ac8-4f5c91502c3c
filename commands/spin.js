const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const database = require('../utils/database');
const minecraftUtils = require('../utils/minecraftUtils');
const rewardDistributor = require('../utils/rewardDistributor');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('spin')
        .setDescription('Use one of your spins to win Minecraft rewards!'),
    category: 'Rewards',

    // Define reward tiers with their probabilities and items
    rewardTiers: {
        common: {
            emoji: '🟢',
            color: '#2ecc71',
            name: 'Common',
            probability: 60, // 60%
            items: [
                'Cooked Mutton ×8',
                'Oak Sapling ×5',
                'Stone Pickaxe (Unbreaking I)',
                'Leather Boots',
                'Glow Ink Sac ×2',
                'Torch ×16',
                'Bread ×3',
                'Apple ×2',
                'Carrot ×5',
                'Pumpkin Pie ×2'
            ]
        },
        rare: {
            emoji: '🔵',
            color: '#3498db',
            name: 'Rare',
            probability: 25, // 25%
            items: [
                'Diamond ×2',
                'Golden Apple ×1',
                'Custom-named Shield',
                'Amethyst Shard ×6',
                'Enchanted Book (Protection I)',
                'Ender Pearl ×3',
                'Iron Ingot ×8',
                'Lapis Lazuli ×8'
            ]
        },
        epic: {
            emoji: '🟣',
            color: '#9b59b6',
            name: 'Epic',
            probability: 12, // 12%
            items: [
                'Totem of Undying ×1',
                'Brewing Stand',
                'Diamond Axe (Efficiency II)',
                'Saddle',
                'Name Tag ×2',
                'Nether Quartz Block ×12'
            ]
        },
        mythical: {
            emoji: '🟡',
            color: '#f1c40f',
            name: 'Mythical',
            probability: 3, // 3%
            items: [
                'Enchanted Elytra (Mending)',
                'Nether Star',
                'Dragon Head',
                'Axolotl Spawn Egg',
                'Beacon'
            ]
        }
    },

    async execute(interaction) {
        try {
            const discordId = interaction.user.id;

            // Find user and check if they have a linked Minecraft account
            const user = await database.findUser(discordId);

            if (!user || !user.minecraftName) {
                // Show linkmc embed with interactive buttons
                const linkEmbed = new EmbedBuilder()
                    .setColor('#4CAF50')
                    .setTitle('🔗 Link Your Account First!')
                    .setDescription('You need to link your Minecraft account before you can spin for rewards!')
                    .addFields(
                        {
                            name: '🎰 Why Link?',
                            value: [
                                '• **Automatic rewards** - Get items directly in-game',
                                '• **Platform detection** - Java or Bedrock support',
                                '• **Secure linking** - Your account, your rewards'
                            ].join('\n'),
                            inline: false
                        },
                        {
                            name: '📝 How to Link',
                            value: 'Click the button below to start linking your account!',
                            inline: false
                        }
                    )
                    .setThumbnail(interaction.user.displayAvatarURL())
                    .setFooter({ text: 'The Basement - Account Required' });

                const linkButton = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`start_linking_${interaction.user.id}`)
                            .setLabel('Link My Account')
                            .setEmoji('🔗')
                            .setStyle(ButtonStyle.Primary)
                    );

                return await interaction.reply({
                    embeds: [linkEmbed],
                    components: [linkButton],
                    ephemeral: true
                });
            }

            // Check if user has spins available
            if (user.spins <= 0) {
                const noSpinsEmbed = new EmbedBuilder()
                    .setColor('#ff6b35')
                    .setTitle('🎰 No Spins Available')
                    .setDescription('You don\'t have any spins left!')
                    .addFields(
                        {
                            name: '💡 How to Get Spins',
                            value: [
                                '🎉 **Invite friends** - Get 10 spins per invite',
                                '👑 **Admin grants** - Ask admins nicely',
                                '🎁 **Special events** - Watch for announcements'
                            ].join('\n'),
                            inline: false
                        },
                        {
                            name: '📊 Your Stats',
                            value: [
                                `**Current Spins:** ${user.spins}`,
                                `**Total Spins Used:** ${user.totalSpins}`,
                                `**Invites Made:** ${user.inviteCount || 0}`
                            ].join('\n'),
                            inline: false
                        }
                    )
                    .setFooter({ text: 'The Basement - Spin System' });

                return await interaction.reply({ embeds: [noSpinsEmbed], ephemeral: true });
            }

            // Defer reply for processing
            await interaction.deferReply();

            // Determine reward tier using weighted random selection
            const rewardTier = rewardDistributor.rollRewardTier();
            const tierData = this.rewardTiers[rewardTier];

            // Deduct spin from user and update rewards
            const updateData = {
                spins: user.spins - 1,
                totalSpins: user.totalSpins + 1
            };

            // Update the specific reward tier count
            const rewardField = `${rewardTier}Rewards`;
            updateData[rewardField] = (user[rewardField] || 0) + 1;

            const updatedUser = await database.updateUser(user.discordId, updateData);

            // Add pending reward for automatic distribution
            const reward = await rewardDistributor.addPendingReward(
                user.discordId,
                user.minecraftName,
                interaction.guild.id,
                rewardTier
            );

            // Get player avatar
            const avatarUrl = await minecraftUtils.getPlayerAvatar(user.minecraftName, 64);
            const displayName = minecraftUtils.formatUsernameForDisplay(user.minecraftName);

            // Create result embed
            const resultEmbed = new EmbedBuilder()
                .setColor(tierData.color)
                .setTitle(`🎰 Spin Results`)
                .setDescription(`${tierData.emoji} **${tierData.name.toUpperCase()} TIER!** ${tierData.emoji}`)
                .setAuthor({
                    name: `${interaction.user.displayName || interaction.user.username} (${displayName})`,
                    iconURL: interaction.user.displayAvatarURL()
                })
                .setThumbnail(avatarUrl)
                .addFields(
                    {
                        name: '🎁 Reward Won',
                        value: `**${reward.item.name}** ×${reward.item.amount || 1}\n\n✨ *This reward will be automatically given to you when you join the Minecraft server!*`,
                        inline: false
                    },
                    {
                        name: '📊 Your Stats',
                        value: [
                            `**Remaining Spins:** ${updatedUser.spins}`,
                            `**Total Spins Used:** ${updatedUser.totalSpins}`,
                            `**${tierData.name} Wins:** ${updatedUser[rewardField]}`
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '🏆 All-Time Rewards',
                        value: [
                            `${this.rewardTiers.mythical.emoji} Mythical: ${updatedUser.mythicalRewards || 0}`,
                            `${this.rewardTiers.epic.emoji} Epic: ${updatedUser.epicRewards || 0}`,
                            `${this.rewardTiers.rare.emoji} Rare: ${updatedUser.rareRewards || 0}`,
                            `${this.rewardTiers.common.emoji} Common: ${updatedUser.commonRewards || 0}`
                        ].join('\n'),
                        inline: true
                    }
                )
                .setTimestamp()
                .setFooter({ text: 'The Basement - Spin System • Rewards distributed automatically when you join!' });

            await interaction.editReply({ embeds: [resultEmbed] });

            // Log the spin result to reward channel
            await this.logSpinResult(interaction, updatedUser, rewardTier, tierData);

            console.log(`🎰 Spin: ${interaction.user.tag} (${updatedUser.minecraftName}) won ${rewardTier} tier`);

        } catch (error) {
            console.error('❌ Error in spin command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#ff6b35')
                .setTitle('❌ Spin Error')
                .setDescription('An error occurred while processing your spin. Please try again later.')
                .setFooter({ text: 'The Basement - Spin System' });

            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },

    /**
     * Select reward tier based on probabilities
     * @returns {string} - Selected reward tier
     */
    selectRewardTier() {
        const random = Math.random() * 100;
        let cumulative = 0;

        // Check in order of rarity (mythical first)
        const tiers = ['mythical', 'epic', 'rare', 'common'];

        for (const tier of tiers) {
            cumulative += this.rewardTiers[tier].probability;
            if (random <= cumulative) {
                return tier;
            }
        }

        // Fallback to common (should never reach here)
        return 'common';
    },

    /**
     * Log spin result to reward channel
     * @param {CommandInteraction} interaction - Discord interaction
     * @param {Object} user - User database object
     * @param {string} rewardTier - Won reward tier
     * @param {Object} tierData - Tier data object
     */
    async logSpinResult(interaction, user, rewardTier, tierData) {
        try {
            const config = await database.findServerConfig(interaction.guild.id);
            if (!config || !config.rewardLogChannel) return;

            const channel = interaction.guild.channels.cache.get(config.rewardLogChannel);
            if (!channel) return;

            const avatarUrl = await minecraftUtils.getPlayerAvatar(user.minecraftName, 32);
            const displayName = minecraftUtils.formatUsernameForDisplay(user.minecraftName);

            const logEmbed = new EmbedBuilder()
                .setColor(tierData.color)
                .setTitle(`🎰 Spin Result - ${tierData.name} Tier`)
                .setDescription(`${tierData.emoji} **${interaction.user.displayName}** won a **${tierData.name}** tier reward!`)
                .setThumbnail(avatarUrl)
                .addFields(
                    {
                        name: '👤 Player Info',
                        value: [
                            `**Discord:** ${interaction.user.tag}`,
                            `**Minecraft:** ${displayName}`,
                            `**Remaining Spins:** ${user.spins}`
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '🎁 Rewards to Distribute',
                        value: tierData.items.map(item => `• ${item}`).join('\n'),
                        inline: false
                    }
                )
                .setTimestamp()
                .setFooter({ text: 'The Basement - Reward Log' });

            await channel.send({ embeds: [logEmbed] });

        } catch (error) {
            console.error('❌ Error logging spin result:', error);
        }
    }
};
