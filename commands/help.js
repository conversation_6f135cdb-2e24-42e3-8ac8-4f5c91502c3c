const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('help')
        .setDescription('Get help with bot commands and features'),
    category: 'General',

    async execute(interaction) {
        try {
            const helpEmbed = new EmbedBuilder()
                .setColor('#4CAF50')
                .setTitle('🏠 The Basement Bot - Help Center')
                .setDescription('Welcome to The Basement! Here are all the available commands:')
                .setThumbnail(interaction.client.user.displayAvatarURL())
                .addFields(
                    {
                        name: '🔗 Account Management',
                        value: [
                            '`/linkmc <minecraft_name>` - Link your Minecraft account',
                            '• Interactive platform selection (Java/Bedrock)',
                            '• **Example:** `/linkmc Steve` then choose your platform'
                        ].join('\n'),
                        inline: false
                    },
                    {
                        name: '🎰 Spin System',
                        value: [
                            '`/spin` - Use a spin to win Minecraft rewards',
                            '• Earn spins by inviting friends (+10 per invite)',
                            '• 4 reward tiers: Common, Rare, Epic, Mythical'
                        ].join('\n'),
                        inline: false
                    },
                    {
                        name: '📢 Reporting',
                        value: [
                            '`/report <description>` - Report issues or violations',
                            '• Reports are sent directly to staff',
                            '• Include as much detail as possible'
                        ].join('\n'),
                        inline: false
                    },
                    {
                        name: '📦 Reward Management',
                        value: [
                            '`/checkrewards` - View your pending rewards',
                            '• Rewards are automatically given when you join the server',
                            '• Check what rewards are waiting for you'
                        ].join('\n'),
                        inline: false
                    },
                    {
                        name: '👑 Admin Commands (Owner/Admin)',
                        value: [
                            '`/grantspin @user <amount>` - Grant spins to users',
                            '`/setupr #channel` - Set reward log channel',
                            '`/setuprp #channel` - Set report channel',
                            '`/setuprcon <host> <port> <password>` - Configure RCON',
                            '`/playerjoin <minecraft_name>` - Trigger reward distribution',
                            '`/createembed` - Create custom embeds'
                        ].join('\n'),
                        inline: false
                    },
                    {
                        name: '🔗 EssentialsXDiscord Integration',
                        value: [
                            '• Link your account first with `/linkmc`',
                            '• Choose Java or Bedrock platform during linking',
                            '• Rewards are automatically distributed when you join',
                            '• Chat bridging handled by EssentialsXDiscord'
                        ].join('\n'),
                        inline: false
                    },
                    {
                        name: '🎁 Reward Tiers',
                        value: [
                            '🟢 **Common (60%)** - Basic items and resources',
                            '🔵 **Rare (25%)** - Valuable items and enchanted books',
                            '🟣 **Epic (12%)** - Rare items and special equipment',
                            '🟡 **Mythical (3%)** - Legendary items and unique rewards'
                        ].join('\n'),
                        inline: false
                    }
                )
                .setTimestamp()
                .setFooter({ text: 'The Basement - Help System' });

            await interaction.reply({ embeds: [helpEmbed] });

        } catch (error) {
            console.error('❌ Error in help command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#ff6b35')
                .setTitle('❌ Help Error')
                .setDescription('An error occurred while loading help information.')
                .setFooter({ text: 'The Basement - Help System' });

            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    }

};
