const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const database = require('../utils/database');
const chatBridge = require('../utils/chatBridge');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('testjoin')
        .setDescription('Test player join event for reward distribution (Owner only)')
        .addStringOption(option =>
            option.setName('minecraft_name')
                .setDescription('Minecraft username to simulate join for')
                .setRequired(true)
        ),
    category: 'Admin',

    async execute(interaction) {
        try {
            // Check if user is the bot owner
            if (interaction.user.id !== process.env.OWNER_ID) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#ff6b35')
                    .setTitle('❌ Access Denied')
                    .setDescription('Only the bot owner can use test commands.')
                    .setFooter({ text: 'The Basement - Test Command' });

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const minecraftName = interaction.options.getString('minecraft_name');

            // Defer reply as this might take a moment
            await interaction.deferReply({ ephemeral: true });

            // Check if this player has pending rewards
            const pendingRewards = await database.getPendingRewards(minecraftName, interaction.guild.id);

            const statusEmbed = new EmbedBuilder()
                .setColor('#2196F3')
                .setTitle('🧪 Testing Player Join Event')
                .setDescription(`Simulating join event for **${minecraftName}**`)
                .addFields({
                    name: '📊 Pending Rewards',
                    value: pendingRewards.length > 0 
                        ? `${pendingRewards.length} pending rewards found`
                        : 'No pending rewards',
                    inline: false
                })
                .setFooter({ text: 'The Basement - Test Command' });

            await interaction.editReply({ embeds: [statusEmbed] });

            // Simulate the player join event
            await chatBridge.handlePlayerJoin(interaction.guild.id, minecraftName);

            // Update the embed with results
            const resultEmbed = new EmbedBuilder()
                .setColor('#4CAF50')
                .setTitle('✅ Player Join Test Complete')
                .setDescription(`Join event processed for **${minecraftName}**`)
                .addFields(
                    {
                        name: '📊 Results',
                        value: [
                            `**Player:** ${minecraftName}`,
                            `**Pending Rewards:** ${pendingRewards.length}`,
                            `**Status:** ${pendingRewards.length > 0 ? 'Rewards distributed' : 'No rewards to distribute'}`
                        ].join('\n'),
                        inline: false
                    },
                    {
                        name: '💡 Note',
                        value: 'Check the chat bridge channel and reward log channel for messages. If RCON is configured, items should be given in-game.',
                        inline: false
                    }
                )
                .setTimestamp()
                .setFooter({ text: 'The Basement - Test Command' });

            // Follow up with the result
            await interaction.followUp({ embeds: [resultEmbed], ephemeral: true });

            console.log(`🧪 Test join event triggered for ${minecraftName} by ${interaction.user.tag}`);

        } catch (error) {
            console.error('❌ Error in testjoin command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#ff6b35')
                .setTitle('❌ Test Error')
                .setDescription('An error occurred while testing the join event.')
                .addFields({
                    name: '🐛 Error Details',
                    value: `\`\`\`${error.message}\`\`\``,
                    inline: false
                })
                .setFooter({ text: 'The Basement - Test Command' });

            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
