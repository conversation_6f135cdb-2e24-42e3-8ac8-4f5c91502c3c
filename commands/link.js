const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const database = require('../utils/database');
const minecraftUtils = require('../utils/minecraftUtils');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('linkmc')
        .setDescription('Link your Discord account to your Minecraft username')
        .addStringOption(option =>
            option.setName('minecraft_name')
                .setDescription('Your Minecraft username (without any prefixes)')
                .setRequired(true)
                .setMaxLength(16)
                .setMinLength(3)
        ),
    category: 'General',

    async execute(interaction) {
        try {
            const minecraftName = interaction.options.getString('minecraft_name');
            const discordId = interaction.user.id;

            // Validate Minecraft username format (without prefix)
            const usernameRegex = /^[a-zA-Z0-9_]{3,16}$/;
            if (!usernameRegex.test(minecraftName)) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#ff6b35')
                    .setTitle('❌ Invalid Username')
                    .setDescription('Please provide a valid Minecraft username.')
                    .addFields(
                        {
                            name: '📋 Username Rules',
                            value: [
                                '• 3-16 characters long',
                                '• Letters, numbers, and underscores only',
                                '• Do not include any prefixes (like `_`)'
                            ].join('\n'),
                            inline: false
                        },
                        {
                            name: '💡 Examples',
                            value: '`Steve`, `Notch`, `Player123`, `CoolGamer_2024`',
                            inline: false
                        }
                    )
                    .setFooter({ text: 'The Basement - Account Linking' });

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Create platform selection embed
            const platformEmbed = new EmbedBuilder()
                .setColor('#4CAF50')
                .setTitle('🎮 Select Your Minecraft Platform')
                .setDescription(`Please select which platform you play **${minecraftName}** on:`)
                .addFields(
                    {
                        name: '☕ Java Edition',
                        value: [
                            '• PC/Mac/Linux versions',
                            '• Purchased from Minecraft.net',
                            '• Supports mods and custom servers',
                            '• Username: `' + minecraftName + '`'
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '🛏️ Bedrock Edition',
                        value: [
                            '• Mobile, Console, Windows 10/11',
                            '• Cross-platform compatible',
                            '• Marketplace content',
                            '• Username: `_' + minecraftName + '`'
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '❓ Not Sure?',
                        value: [
                            '• **Java** if you play on PC with mods',
                            '• **Bedrock** if you play on mobile/console',
                            '• **Bedrock** if you have Xbox Game Pass'
                        ].join('\n'),
                        inline: false
                    }
                )
                .setThumbnail(interaction.user.displayAvatarURL())
                .setFooter({ text: 'The Basement - Platform Selection' });

            // Create buttons
            const buttons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`link_java_${minecraftName}_${discordId}`)
                        .setLabel('Java Edition')
                        .setEmoji('☕')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId(`link_bedrock_${minecraftName}_${discordId}`)
                        .setLabel('Bedrock Edition')
                        .setEmoji('🛏️')
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId(`link_cancel_${discordId}`)
                        .setLabel('Cancel')
                        .setEmoji('❌')
                        .setStyle(ButtonStyle.Danger)
                );

            await interaction.reply({
                embeds: [platformEmbed],
                components: [buttons],
                ephemeral: true
            });

        } catch (error) {
            console.error('❌ Error in linkmc command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#ff6b35')
                .setTitle('❌ Error')
                .setDescription('An error occurred while processing your request. Please try again later.')
                .setFooter({ text: 'The Basement - Account Linking' });

            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },

    /**
     * Handle button interactions for platform selection
     * @param {ButtonInteraction} interaction - Button interaction
     */
    async handleButtonInteraction(interaction) {
        try {
            const [action, platform, minecraftName, discordId] = interaction.customId.split('_');

            // Verify the interaction is for the correct user
            if (discordId !== interaction.user.id) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#ff6b35')
                    .setTitle('❌ Access Denied')
                    .setDescription('You can only link your own account.')
                    .setFooter({ text: 'The Basement - Account Linking' });

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            if (action === 'link' && platform === 'cancel') {
                const cancelEmbed = new EmbedBuilder()
                    .setColor('#9E9E9E')
                    .setTitle('❌ Account Linking Cancelled')
                    .setDescription('Account linking has been cancelled. You can try again anytime with `/linkmc`.')
                    .setFooter({ text: 'The Basement - Account Linking' });

                return await interaction.update({ embeds: [cancelEmbed], components: [] });
            }

            await interaction.deferUpdate();

            // Determine final username based on platform
            let finalUsername = minecraftName;
            let platformName = 'Java Edition';

            if (platform === 'bedrock') {
                // INACTIVE LINE: Uncomment the line below to enable Bedrock prefix
                // finalUsername = `_${minecraftName}`;
                platformName = 'Bedrock Edition';
            }

            // Check if this username is already linked to another Discord account
            const existingUsers = await database.query(
                'SELECT * FROM users WHERE minecraftName = ? AND discordId != ?',
                [finalUsername, interaction.user.id]
            );

            if (existingUsers.length > 0) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#ff6b35')
                    .setTitle('❌ Username Already Linked')
                    .setDescription(`The username **${finalUsername}** is already linked to another Discord account.`)
                    .setFooter({ text: 'The Basement - Account Linking' });

                return await interaction.editReply({ embeds: [errorEmbed], components: [] });
            }

            // Find or create user record
            let user = await database.findUser(interaction.user.id);
            let isUpdate = false;

            if (!user) {
                user = await database.createUser(interaction.user.id, {
                    minecraftName: finalUsername
                });
            } else {
                const oldUsername = user.minecraftName;
                user = await database.updateUser(interaction.user.id, {
                    minecraftName: finalUsername
                });
                isUpdate = !!oldUsername;
            }

            // Get Minecraft avatar
            const avatarUrl = await minecraftUtils.getPlayerAvatar(finalUsername, 128);
            const displayName = minecraftUtils.formatUsernameForDisplay(finalUsername);

            // Create success embed
            const successEmbed = new EmbedBuilder()
                .setColor('#4CAF50')
                .setTitle('✅ Account Linked Successfully!')
                .setDescription(`Your Discord account has been linked to **${finalUsername}**`)
                .setAuthor({
                    name: interaction.user.displayName || interaction.user.username,
                    iconURL: interaction.user.displayAvatarURL()
                })
                .setThumbnail(avatarUrl)
                .addFields(
                    {
                        name: '🎮 Player Info',
                        value: [
                            `**Username:** ${displayName}`,
                            `**Platform:** ${platformName}`,
                            `**Full Name:** ${finalUsername}`
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '🎁 Your Stats',
                        value: [
                            `**Spins:** ${user.spins}`,
                            `**Total Spins Used:** ${user.totalSpins}`,
                            `**Invites:** ${user.inviteCount || 0}`
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '🚀 What\'s Next?',
                        value: [
                            '🎰 Use `/spin` to play the slot machine',
                            '💬 Chat in the bridge channel',
                            '🎉 Invite friends to earn more spins!'
                        ].join('\n'),
                        inline: false
                    }
                )
                .setTimestamp()
                .setFooter({ text: 'The Basement - Account Linking' });

            if (isUpdate) {
                successEmbed.setTitle('✅ Account Updated Successfully!');
                successEmbed.setDescription(`Your linked Minecraft account has been updated to **${finalUsername}**`);
            }

            await interaction.editReply({ embeds: [successEmbed], components: [] });

            console.log(`✅ Account ${isUpdate ? 'updated' : 'linked'}: ${interaction.user.tag} → ${finalUsername} (${platformName})`);

        } catch (error) {
            console.error('❌ Error in linkmc button interaction:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#ff6b35')
                .setTitle('❌ Error')
                .setDescription('An error occurred while linking your account. Please try again later.')
                .addFields({
                    name: '🆘 Need Help?',
                    value: 'Join our Discord server: https://discord.gg/GCNHctBhk9',
                    inline: false
                })
                .setFooter({ text: 'The Basement - Account Linking' });

            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed], components: [] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
