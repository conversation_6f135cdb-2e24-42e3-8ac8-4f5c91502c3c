const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const database = require('../utils/database');
const playerJoinHandler = require('../utils/playerJoinHandler');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('playerjoin')
        .setDescription('Trigger reward distribution for a player who joined the server (Admin only)')
        .addStringOption(option =>
            option.setName('minecraft_name')
                .setDescription('Minecraft username of the player who joined')
                .setRequired(true)
        )
        .addBooleanOption(option =>
            option.setName('silent')
                .setDescription('Whether to suppress the response message (default: false)')
                .setRequired(false)
        ),
    category: 'Admin',

    async execute(interaction) {
        try {
            // Check if user has admin permissions or is the bot owner
            const isOwner = interaction.user.id === process.env.OWNER_ID;
            const hasAdminPerms = interaction.member?.permissions?.has('Administrator');

            if (!isOwner && !hasAdminPerms) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#ff6b35')
                    .setTitle('❌ Access Denied')
                    .setDescription('You need Administrator permissions or be the bot owner to use this command.')
                    .setFooter({ text: 'The Basement - Player Join' });

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const minecraftName = interaction.options.getString('minecraft_name');
            const silent = interaction.options.getBoolean('silent') || false;

            // Check if this player has pending rewards first
            const pendingCount = await playerJoinHandler.getPendingRewardsCount(minecraftName, interaction.guild.id);

            if (silent) {
                // For silent mode, just acknowledge and process in background
                await interaction.reply({ 
                    content: `🎮 Processing join for **${minecraftName}**...`, 
                    ephemeral: true 
                });

                // Process the join
                await playerJoinHandler.handlePlayerJoin(interaction.guild.id, minecraftName);
                return;
            }

            // Defer reply for non-silent mode
            await interaction.deferReply({ ephemeral: true });

            // Process the player join
            const success = await playerJoinHandler.handlePlayerJoin(interaction.guild.id, minecraftName);

            // Create response embed
            const embed = new EmbedBuilder()
                .setTimestamp()
                .setFooter({ text: 'The Basement - Player Join' });

            if (success && pendingCount > 0) {
                embed
                    .setColor('#4CAF50')
                    .setTitle('✅ Player Join Processed')
                    .setDescription(`Successfully processed join for **${minecraftName}**`)
                    .addFields(
                        {
                            name: '🎁 Rewards Distributed',
                            value: `${pendingCount} pending rewards were automatically given to the player`,
                            inline: false
                        },
                        {
                            name: '📊 Status',
                            value: [
                                '✅ RCON connection successful',
                                '✅ Items delivered in-game',
                                '✅ Player notified',
                                '✅ Logged to reward channel'
                            ].join('\n'),
                            inline: false
                        }
                    );
            } else if (success && pendingCount === 0) {
                embed
                    .setColor('#9E9E9E')
                    .setTitle('ℹ️ Player Join Processed')
                    .setDescription(`Processed join for **${minecraftName}** - no pending rewards`)
                    .addFields({
                        name: '📊 Status',
                        value: 'Player had no pending rewards to distribute',
                        inline: false
                    });
            } else {
                embed
                    .setColor('#ff6b35')
                    .setTitle('❌ Processing Failed')
                    .setDescription(`Failed to process join for **${minecraftName}**`)
                    .addFields(
                        {
                            name: '🔍 Possible Issues',
                            value: [
                                '• RCON connection failed',
                                '• Player name not found in database',
                                '• Server connection issues',
                                '• Invalid player name format'
                            ].join('\n'),
                            inline: false
                        },
                        {
                            name: '🛠️ Troubleshooting',
                            value: [
                                '• Check RCON configuration with `/setuprcon`',
                                '• Verify player has linked their account',
                                '• Ensure Minecraft server is online',
                                '• Check console logs for detailed errors'
                            ].join('\n'),
                            inline: false
                        }
                    );
            }

            await interaction.editReply({ embeds: [embed] });

            console.log(`🎮 Player join triggered for ${minecraftName} by ${interaction.user.tag} - Success: ${success}, Rewards: ${pendingCount}`);

        } catch (error) {
            console.error('❌ Error in playerjoin command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#ff6b35')
                .setTitle('❌ Command Error')
                .setDescription('An error occurred while processing the player join.')
                .addFields({
                    name: '🐛 Error Details',
                    value: `\`\`\`${error.message}\`\`\``,
                    inline: false
                })
                .setFooter({ text: 'The Basement - Player Join' });

            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
