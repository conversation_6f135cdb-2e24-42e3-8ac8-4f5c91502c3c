const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const database = require('../utils/database');
const rconManager = require('../utils/rconManager');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setuprcon')
        .setDescription('Configure RCON connection for automatic reward distribution (Owner only)')
        .addStringOption(option =>
            option.setName('host')
                .setDescription('Minecraft server IP address')
                .setRequired(true)
        )
        .addIntegerOption(option =>
            option.setName('port')
                .setDescription('RCON port (default: 25575)')
                .setRequired(false)
                .setMinValue(1)
                .setMaxValue(65535)
        )
        .addStringOption(option =>
            option.setName('password')
                .setDescription('RCON password')
                .setRequired(true)
        ),
    category: 'Admin',

    async execute(interaction) {
        try {
            // Check if user is the bot owner
            if (interaction.user.id !== process.env.OWNER_ID) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#ff6b35')
                    .setTitle('❌ Access Denied')
                    .setDescription('Only the bot owner can configure RCON settings.')
                    .setFooter({ text: 'The Basement - RCON Setup' });

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const host = interaction.options.getString('host');
            const port = interaction.options.getInteger('port') || 25575;
            const password = interaction.options.getString('password');

            // Defer reply as this might take a moment
            await interaction.deferReply({ ephemeral: true });

            // Set RCON configuration
            rconManager.setRconConfig(interaction.guild.id, {
                host,
                port,
                password
            });

            // Test the connection
            const connected = await rconManager.connect(interaction.guild.id);

            if (connected) {
                // Test with a simple command
                const testResponse = await rconManager.sendCommand(interaction.guild.id, 'list');
                
                const successEmbed = new EmbedBuilder()
                    .setColor('#4CAF50')
                    .setTitle('✅ RCON Configuration Successful')
                    .setDescription('RCON has been configured and tested successfully!')
                    .addFields(
                        {
                            name: '🔧 Configuration',
                            value: [
                                `**Host:** ${host}`,
                                `**Port:** ${port}`,
                                `**Status:** Connected ✅`
                            ].join('\n'),
                            inline: true
                        },
                        {
                            name: '🎁 Features Enabled',
                            value: [
                                '• Automatic reward distribution',
                                '• Player join detection',
                                '• In-game notifications',
                                '• Chat bridge integration'
                            ].join('\n'),
                            inline: true
                        }
                    )
                    .setFooter({ text: 'The Basement - RCON Setup' });

                if (testResponse) {
                    successEmbed.addFields({
                        name: '🧪 Test Response',
                        value: `\`\`\`${testResponse.substring(0, 100)}${testResponse.length > 100 ? '...' : ''}\`\`\``,
                        inline: false
                    });
                }

                await interaction.editReply({ embeds: [successEmbed] });

                console.log(`✅ RCON configured for guild ${interaction.guild.id}: ${host}:${port}`);

            } else {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#ff6b35')
                    .setTitle('❌ RCON Connection Failed')
                    .setDescription('Failed to connect to the Minecraft server with the provided settings.')
                    .addFields(
                        {
                            name: '🔧 Configuration Attempted',
                            value: [
                                `**Host:** ${host}`,
                                `**Port:** ${port}`,
                                `**Status:** Failed ❌`
                            ].join('\n'),
                            inline: true
                        },
                        {
                            name: '🛠️ Troubleshooting',
                            value: [
                                '• Check if RCON is enabled in server.properties',
                                '• Verify the RCON password is correct',
                                '• Ensure the port is not blocked by firewall',
                                '• Confirm the server IP address is correct'
                            ].join('\n'),
                            inline: false
                        },
                        {
                            name: '📋 Server.properties Example',
                            value: [
                                '```properties',
                                'enable-rcon=true',
                                `rcon.port=${port}`,
                                'rcon.password=your_password_here',
                                '```'
                            ].join('\n'),
                            inline: false
                        }
                    )
                    .setFooter({ text: 'The Basement - RCON Setup' });

                await interaction.editReply({ embeds: [errorEmbed] });
            }

        } catch (error) {
            console.error('❌ Error in setuprcon command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#ff6b35')
                .setTitle('❌ Setup Error')
                .setDescription('An error occurred while setting up RCON. Please try again later.')
                .addFields({
                    name: '🐛 Error Details',
                    value: `\`\`\`${error.message}\`\`\``,
                    inline: false
                })
                .setFooter({ text: 'The Basement - RCON Setup' });

            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
