const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const database = require('../utils/database');
const minecraftUtils = require('../utils/minecraftUtils');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('checkrewards')
        .setDescription('Check your pending rewards that will be given when you join the server'),
    category: 'General',

    async execute(interaction) {
        try {
            const discordId = interaction.user.id;

            // Find user and check if they have a linked Minecraft account
            const user = await database.findUser(discordId);

            if (!user || !user.minecraftName) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#ff6b35')
                    .setTitle('🔗 Account Not Linked')
                    .setDescription('You need to link your Minecraft account first!')
                    .addFields({
                        name: 'How to Link',
                        value: 'Use `/linkmc <minecraft_name>` to link your account and choose your platform!',
                        inline: false
                    })
                    .setFooter({ text: 'The Basement - Pending Rewards' });

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Get pending rewards
            const pendingRewards = await database.getPendingRewards(user.minecraftName, interaction.guild.id);

            // Get player avatar
            const avatarUrl = await minecraftUtils.getPlayerAvatar(user.minecraftName, 64);
            const displayName = minecraftUtils.formatUsernameForDisplay(user.minecraftName);

            if (pendingRewards.length === 0) {
                const noRewardsEmbed = new EmbedBuilder()
                    .setColor('#9E9E9E')
                    .setTitle('📦 No Pending Rewards')
                    .setDescription(`You don't have any pending rewards, **${displayName}**!`)
                    .setThumbnail(avatarUrl)
                    .addFields({
                        name: '🎰 Want Rewards?',
                        value: 'Use `/spin` to play the slot machine and win rewards!',
                        inline: false
                    })
                    .setFooter({ text: 'The Basement - Pending Rewards' });

                return await interaction.reply({ embeds: [noRewardsEmbed], ephemeral: true });
            }

            // Group rewards by tier
            const rewardsByTier = {};
            const rewardTiers = {
                common: { emoji: '📦', color: '#9E9E9E' },
                rare: { emoji: '💎', color: '#2196F3' },
                epic: { emoji: '🔮', color: '#9C27B0' },
                mythical: { emoji: '👑', color: '#FFD700' }
            };

            for (const reward of pendingRewards) {
                if (!rewardsByTier[reward.rewardTier]) {
                    rewardsByTier[reward.rewardTier] = [];
                }
                rewardsByTier[reward.rewardTier].push(reward);
            }

            // Create main embed
            const rewardsEmbed = new EmbedBuilder()
                .setColor('#4CAF50')
                .setTitle('🎁 Your Pending Rewards')
                .setDescription(`You have **${pendingRewards.length}** pending rewards, **${displayName}**!\n\n✨ *These will be automatically given to you when you join the Minecraft server.*`)
                .setThumbnail(avatarUrl)
                .setTimestamp()
                .setFooter({ text: 'The Basement - Pending Rewards' });

            // Add fields for each tier
            for (const [tier, rewards] of Object.entries(rewardsByTier)) {
                const tierData = rewardTiers[tier];
                const itemList = rewards.map(reward => {
                    const items = reward.rewardItems.map(item => 
                        `${item.amount || 1}x ${item.name || item.item.replace('minecraft:', '')}`
                    ).join(', ');
                    return `• ${items}`;
                }).join('\n');

                rewardsEmbed.addFields({
                    name: `${tierData.emoji} ${tier.charAt(0).toUpperCase() + tier.slice(1)} Rewards (${rewards.length})`,
                    value: itemList || 'No items',
                    inline: false
                });
            }

            // Add summary field
            const summary = Object.entries(rewardsByTier).map(([tier, rewards]) => {
                const tierData = rewardTiers[tier];
                return `${tierData.emoji} ${rewards.length} ${tier}`;
            }).join(' • ');

            rewardsEmbed.addFields({
                name: '📊 Summary',
                value: summary,
                inline: false
            });

            await interaction.reply({ embeds: [rewardsEmbed], ephemeral: true });

            console.log(`📦 ${interaction.user.tag} checked pending rewards: ${pendingRewards.length} rewards`);

        } catch (error) {
            console.error('❌ Error in checkrewards command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#ff6b35')
                .setTitle('❌ Error')
                .setDescription('An error occurred while checking your pending rewards. Please try again later.')
                .setFooter({ text: 'The Basement - Pending Rewards' });

            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    }
};
