# 🚀 The Basement Bot - Complete Setup Guide

## 📋 Prerequisites

### Discord Setup
1. **Discord Bot Token** - Create a bot at [Discord Developer Portal](https://discord.com/developers/applications)
2. **Bot Permissions** - Your bot needs these permissions:
   - Send Messages
   - Use Slash Commands
   - Embed Links
   - Read Message History
   - Add Reactions (for reaction roles)

### Minecraft Server Setup
1. **RCON Enabled** - Required for automatic reward distribution
2. **Server Access** - Bo<PERSON> needs network access to your Minecraft server
3. **Admin Permissions** - <PERSON><PERSON> will execute commands as admin
4. **EssentialsXDiscord** - Recommended for chat bridging (optional)

## 🔧 Environment Configuration

Create a `.env` file in your bot directory:

```env
# Discord Bot Configuration
BOT_TOKEN=your_discord_bot_token_here
CLIENT_ID=your_discord_application_client_id

# Bot Configuration  
OWNER_ID=your_owner_discord_id_here

# Optional: Guild ID for faster command registration during development
GUILD_ID=your_discord_server_id
```

## 🎮 Minecraft Server Configuration

### Enable RCON in server.properties
```properties
# Enable RCON
enable-rcon=true
rcon.port=25575
rcon.password=your_secure_password_here

# Optional: Restrict RCON to localhost for security
rcon.ip=127.0.0.1
```

### Firewall Configuration
- Open RCON port (default: 25575) for bot access
- Consider restricting access to bot's IP address only

## 🚀 Bot Setup Steps

### 1. Install Dependencies
```bash
npm install
```

### 2. Start the Bot
```bash
npm start
```

### 3. Configure Bot in Discord

#### Set Up Channels (Owner Only)
```
/setupr #reward-logs         - Set reward log channel
/setuprp #reports           - Set report channel
```

#### Configure RCON Connection (Owner Only)
```
/setuprcon host:your-server-ip port:25575 password:your-rcon-password
```

### 4. Test the Setup

#### Test RCON Connection
The `/setuprcon` command will automatically test the connection and show results.

#### Test Reward Distribution
```
/testjoin minecraft_name:TestPlayer
```

## 🎯 How It Works

### 1. Account Linking Process
1. User runs `/spin` without linked account
2. Bot shows interactive linking interface
3. User provides Minecraft username
4. User selects platform (Java/Bedrock)
5. Account is linked and ready for rewards

### 2. Reward Distribution Flow
1. User spins and wins reward
2. Reward is stored as "pending" in database
3. When player joins Minecraft server:
   - Admin or EssentialsXDiscord triggers `/playerjoin <minecraft_name>`
   - Bot automatically gives pending rewards via RCON
   - Sends confirmation to reward log channel
   - Notifies player in-game

### 3. Platform Support
- **Java Edition**: Username stored as-is (e.g., `Steve`)
- **Bedrock Edition**: Username prefixed with underscore (e.g., `_Steve`)
- **Automatic Detection**: Bot handles platform differences automatically

## 🛠️ Available Commands

### User Commands
- `/linkmc <minecraft_name>` - Link Minecraft account with platform selection
- `/spin` - Play slot machine (requires linked account)
- `/checkrewards` - View pending rewards
- `/report <description>` - Report issues
- `/help` - Show help information

### Admin Commands (Owner/Admin)
- `/grantspin @user <amount>` - Grant spins to users (Owner only)
- `/setupr #channel` - Configure reward log channel (Owner only)
- `/setuprp #channel` - Configure report channel (Owner only)
- `/setuprcon <host> <port> <password>` - Configure RCON connection (Owner only)
- `/playerjoin <minecraft_name>` - Trigger reward distribution for player joins
- `/testjoin <minecraft_name>` - Test reward distribution system (Owner only)
- `/createembed` - Create custom embeds (Owner only)

## 🔍 Troubleshooting

### RCON Connection Issues
1. **Check server.properties** - Ensure RCON is enabled
2. **Verify password** - Must match exactly
3. **Check firewall** - Ensure port is accessible
4. **Test manually** - Use RCON client to verify connection

### Reward Distribution Issues
1. **Check RCON status** - Use `/setuprcon` to test connection
2. **Verify player names** - Must match exactly (case-sensitive)
3. **Check logs** - Bot logs all reward distribution attempts
4. **Test manually** - Use `/testjoin` to simulate player join

### Account Linking Issues
1. **Username format** - 3-16 characters, alphanumeric + underscores
2. **Platform selection** - Ensure correct platform is chosen
3. **Duplicate accounts** - Each Minecraft name can only link to one Discord account

## 📊 Database Schema

The bot uses SQLite with these main tables:

### Users Table
- `discordId` - Discord user ID (unique)
- `minecraftName` - Linked Minecraft username
- `spins` - Available spins
- `totalSpins` - Total spins used
- Reward tier counters

### Pending Rewards Table
- `discordId` - Discord user ID
- `minecraftName` - Minecraft username
- `rewardTier` - Reward tier (common/rare/epic/mythical)
- `rewardItems` - JSON array of items to give
- `distributed` - Whether reward has been given

## 🔒 Security Considerations

1. **RCON Password** - Use a strong, unique password
2. **Network Access** - Restrict RCON port access when possible
3. **Owner Commands** - Only bot owner can configure sensitive settings
4. **Input Validation** - All user inputs are validated and sanitized

## 📈 Monitoring

### Log Messages
- ✅ Successful operations
- ❌ Errors and failures
- 🎰 Spin results
- 🎁 Reward distributions
- 🔌 RCON connection status

### Discord Notifications
- Reward log channel shows all distributions
- Player join notifications when rewards are distributed
- Error messages sent to appropriate channels

## 🆘 Support

If you encounter issues:
1. Check the console logs for error messages
2. Verify all configuration steps
3. Test individual components (RCON, linking, etc.)
4. **Join our Discord server for help**: https://discord.gg/GCNHctBhk9
5. Contact the bot owner or create an issue

---

## 🔗 EssentialsXDiscord Integration

If you're using EssentialsXDiscord for chat bridging, you can integrate it with The Basement Bot:

### Option 1: Manual Integration
- Use `/playerjoin <minecraft_name>` command when players join
- Can be triggered by server admins or through custom scripts

### Option 2: Automated Integration (Advanced)
- Set up EssentialsXDiscord to call `/playerjoin` via Discord webhook
- Configure player join events to trigger reward distribution
- Requires custom configuration in EssentialsXDiscord

### Configuration Example
```yaml
# In EssentialsXDiscord config
player-join:
  enabled: true
  webhook-url: "your-discord-webhook-url"
  command: "/playerjoin {player}"
```

**🎉 Congratulations!** Your bot is now set up for automatic reward distribution. Players can link their accounts, spin for rewards, and receive them when they join your Minecraft server!
