# The Basement Bot

A comprehensive Discord bot for Minecraft server integration supporting both Java and Bedrock players. Features account linking, spin-based reward system, invite tracking, chat bridging, and administrative tools.

## Features

### 🔗 Account Linking
- Link Discord accounts to Minecraft usernames
- Support for both Java and Bedrock players
- Bedrock players use underscore prefix (e.g., `_PlayerName`)

### 🎰 Spin Reward System
- **Automatic reward distribution** via RCON integration
- **Account linking required** - Players must link before spinning
- 4 reward tiers with dynamic probabilities:
  - **Common (60%)** - Basic items and resources
  - **Rare (25%)** - Valuable items and enchanted books
  - **Epic (12%)** - Rare items and special equipment
  - **Mythical (3%)** - Legendary items and unique rewards
- **Pending rewards** - Distributed automatically when players join server

### 🎉 Invite Tracking
- Automatic detection of new member invites
- +10 spins reward per successful invite
- Comprehensive invite statistics

### 💬 Chat Bridge
- Real-time chat between Minecraft and Discord
- Rich embeds with player avatars
- Support for both Java and Bedrock players
- Automatic platform detection

### 👑 Administrative Tools
- Custom embed creation with color validation
- Poll creation system
- Reaction role management
- Comprehensive reporting system
- Owner-only configuration commands

## Commands

### General Commands
- `/linkmc <minecraft_name>` - Link your Minecraft account
- `/spin` - Use a spin to win rewards
- `/report <description>` - Report issues or violations
- `/help` - Show help information

### Admin Commands (Owner Only)
- `/grantspin @user <amount>` - Grant spins to users
- `/setupcb #channel` - Set chat bridge channel
- `/setupr #channel` - Set reward log channel
- `/setuprp #channel` - Set report channel
- `/setuprcon <host> <port> <password>` - Configure RCON for automatic rewards
- `/createembed` - Create custom embeds

## Setup

### Prerequisites
- Node.js 16.9.0 or higher
- Discord bot token
- Discord application with slash commands enabled

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd the-basement-bot
```

2. Install dependencies
```bash
npm install
```

3. Configure environment variables
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Start the bot
```bash
npm start
```

### Environment Variables

```env
# Discord Bot Configuration
BOT_TOKEN=your_discord_bot_token_here
CLIENT_ID=your_discord_application_client_id

# Bot Configuration
OWNER_ID=your_owner_discord_id_here

# Optional: Guild ID for faster command registration during development
GUILD_ID=your_discord_server_id
```

## Database Schema (SQLite)

### Users Table
- `discordId` - Discord user ID (unique)
- `minecraftName` - Linked Minecraft username
- `spins` - Available spins
- `totalSpins` - Total spins used
- `commonRewards`, `rareRewards`, `epicRewards`, `mythicalRewards` - Reward tier counts
- `inviteCount` - Number of successful invites
- `invitedBy` - Who invited this user

### ServerConfigs Table
- `guildId` - Discord server ID (unique)
- `chatBridgeChannel` - Chat bridge channel ID
- `rewardLogChannel` - Reward log channel ID
- `reportChannel` - Report channel ID
- `ownerId` - Server owner ID

### Invites Table
- `guildId` - Discord server ID
- `inviteCode` - Invite code
- `inviterId` - User who created invite
- `uses` - Current usage count

## Architecture

### Modular Structure
```
├── commands/           # Slash command implementations
├── data/              # SQLite database file
├── utils/             # Utility modules
│   ├── database.js          # SQLite database handler
│   ├── commandHandler.js    # Command registration and handling
│   ├── inviteTracker.js     # Invite tracking system
│   ├── chatBridge.js        # Chat bridging functionality
│   └── minecraftUtils.js    # Minecraft-related utilities
└── index.js           # Main bot entry point
```

### Key Features
- **Event-driven architecture** using discord.js v14
- **Persistent data storage** with SQLite (no external database required)
- **Modular command system** with automatic registration
- **Comprehensive error handling** and logging
- **Security-focused** with owner-only sensitive commands

## Minecraft Integration

### Chat Bridge
- Messages from Discord bridge channel are forwarded to Minecraft
- Minecraft messages appear as rich embeds in Discord
- Player avatars fetched from Crafatar/MC-Heads APIs
- Platform detection (Java vs Bedrock)

### Reward System
- Spin results logged to reward channel for manual distribution
- Detailed reward specifications for each tier
- Admin tools for spin management
- Comprehensive statistics tracking

## Security

- All sensitive commands restricted to bot owner
- Input validation for hex colors, URLs, and usernames
- Permission checks for channel access
- Comprehensive audit logging
- Error handling prevents information leakage

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For support, please contact the bot owner or create an issue in the repository.
