const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class Database {
    constructor() {
        this.db = null;
        this.dbPath = path.join(__dirname, '..', 'data', 'basement.db');
    }

    /**
     * Initialize the database connection and create tables
     */
    async initialize() {
        return new Promise((resolve, reject) => {
            // Create data directory if it doesn't exist
            const fs = require('fs');
            const dataDir = path.dirname(this.dbPath);
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }

            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    console.error('❌ Error opening database:', err);
                    reject(err);
                } else {
                    console.log('✅ Connected to SQLite database');
                    this.createTables().then(resolve).catch(reject);
                }
            });
        });
    }

    /**
     * Create all necessary tables
     */
    async createTables() {
        return new Promise((resolve, reject) => {
            const tables = [
                // Users table
                `CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    discordId TEXT UNIQUE NOT NULL,
                    minecraftName TEXT,
                    spins INTEGER DEFAULT 0,
                    totalSpins INTEGER DEFAULT 0,
                    commonRewards INTEGER DEFAULT 0,
                    rareRewards INTEGER DEFAULT 0,
                    epicRewards INTEGER DEFAULT 0,
                    mythicalRewards INTEGER DEFAULT 0,
                    invitedBy TEXT,
                    inviteCount INTEGER DEFAULT 0,
                    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )`,

                // Server configurations table
                `CREATE TABLE IF NOT EXISTS serverConfigs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    guildId TEXT UNIQUE NOT NULL,
                    rewardLogChannel TEXT,
                    reportChannel TEXT,
                    ownerId TEXT NOT NULL,
                    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )`,

                // Invites table
                `CREATE TABLE IF NOT EXISTS invites (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    guildId TEXT NOT NULL,
                    inviteCode TEXT NOT NULL,
                    inviterId TEXT NOT NULL,
                    uses INTEGER DEFAULT 0,
                    maxUses INTEGER DEFAULT 0,
                    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(guildId, inviteCode)
                )`,

                // Pending rewards table
                `CREATE TABLE IF NOT EXISTS pendingRewards (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    discordId TEXT NOT NULL,
                    minecraftName TEXT NOT NULL,
                    guildId TEXT NOT NULL,
                    rewardTier TEXT NOT NULL,
                    rewardItems TEXT NOT NULL,
                    distributed BOOLEAN DEFAULT FALSE,
                    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    distributedAt DATETIME
                )`
            ];

            let completed = 0;
            const total = tables.length;

            tables.forEach((sql, index) => {
                this.db.run(sql, (err) => {
                    if (err) {
                        console.error(`❌ Error creating table ${index + 1}:`, err);
                        reject(err);
                    } else {
                        completed++;
                        if (completed === total) {
                            console.log('✅ All database tables created successfully');
                            resolve();
                        }
                    }
                });
            });
        });
    }

    /**
     * Find user by Discord ID
     * @param {string} discordId - Discord user ID
     * @returns {Promise<Object|null>} - User object or null
     */
    async findUser(discordId) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT * FROM users WHERE discordId = ?',
                [discordId],
                (err, row) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(row || null);
                    }
                }
            );
        });
    }

    /**
     * Find or create user
     * @param {string} discordId - Discord user ID
     * @returns {Promise<Object>} - User object
     */
    async findOrCreateUser(discordId) {
        let user = await this.findUser(discordId);
        if (!user) {
            user = await this.createUser(discordId);
        }
        return user;
    }

    /**
     * Create a new user
     * @param {string} discordId - Discord user ID
     * @param {Object} data - Additional user data
     * @returns {Promise<Object>} - Created user object
     */
    async createUser(discordId, data = {}) {
        return new Promise((resolve, reject) => {
            const {
                minecraftName = null,
                spins = 0,
                totalSpins = 0,
                invitedBy = null,
                inviteCount = 0
            } = data;

            this.db.run(
                `INSERT INTO users (discordId, minecraftName, spins, totalSpins, invitedBy, inviteCount)
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [discordId, minecraftName, spins, totalSpins, invitedBy, inviteCount],
                function(err) {
                    if (err) {
                        reject(err);
                    } else {
                        // Return the created user
                        resolve({
                            id: this.lastID,
                            discordId,
                            minecraftName,
                            spins,
                            totalSpins,
                            commonRewards: 0,
                            rareRewards: 0,
                            epicRewards: 0,
                            mythicalRewards: 0,
                            invitedBy,
                            inviteCount,
                            createdAt: new Date().toISOString(),
                            updatedAt: new Date().toISOString()
                        });
                    }
                }
            );
        });
    }

    /**
     * Update user data
     * @param {string} discordId - Discord user ID
     * @param {Object} data - Data to update
     * @returns {Promise<Object>} - Updated user object
     */
    async updateUser(discordId, data) {
        return new Promise((resolve, reject) => {
            const fields = [];
            const values = [];

            // Build dynamic update query
            Object.keys(data).forEach(key => {
                if (key !== 'id' && key !== 'discordId' && key !== 'createdAt') {
                    fields.push(`${key} = ?`);
                    values.push(data[key]);
                }
            });

            if (fields.length === 0) {
                return this.findUser(discordId).then(resolve).catch(reject);
            }

            fields.push('updatedAt = ?');
            values.push(new Date().toISOString());
            values.push(discordId);

            const sql = `UPDATE users SET ${fields.join(', ')} WHERE discordId = ?`;

            this.db.run(sql, values, (err) => {
                if (err) {
                    reject(err);
                } else {
                    this.findUser(discordId).then(resolve).catch(reject);
                }
            });
        });
    }

    /**
     * Find server configuration
     * @param {string} guildId - Guild ID
     * @returns {Promise<Object|null>} - Server config or null
     */
    async findServerConfig(guildId) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT * FROM serverConfigs WHERE guildId = ?',
                [guildId],
                (err, row) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(row || null);
                    }
                }
            );
        });
    }

    /**
     * Find or create server configuration
     * @param {string} guildId - Guild ID
     * @param {string} ownerId - Owner ID
     * @returns {Promise<Object>} - Server config object
     */
    async findOrCreateServerConfig(guildId, ownerId) {
        let config = await this.findServerConfig(guildId);
        if (!config) {
            config = await this.createServerConfig(guildId, ownerId);
        }
        return config;
    }

    /**
     * Create server configuration
     * @param {string} guildId - Guild ID
     * @param {string} ownerId - Owner ID
     * @returns {Promise<Object>} - Created server config
     */
    async createServerConfig(guildId, ownerId) {
        return new Promise((resolve, reject) => {
            this.db.run(
                `INSERT INTO serverConfigs (guildId, ownerId) VALUES (?, ?)`,
                [guildId, ownerId],
                function(err) {
                    if (err) {
                        reject(err);
                    } else {
                        resolve({
                            id: this.lastID,
                            guildId,
                            rewardLogChannel: null,
                            reportChannel: null,
                            ownerId,
                            createdAt: new Date().toISOString(),
                            updatedAt: new Date().toISOString()
                        });
                    }
                }
            );
        });
    }

    /**
     * Update server configuration
     * @param {string} guildId - Guild ID
     * @param {Object} data - Data to update
     * @returns {Promise<Object>} - Updated server config
     */
    async updateServerConfig(guildId, data) {
        return new Promise((resolve, reject) => {
            const fields = [];
            const values = [];

            Object.keys(data).forEach(key => {
                if (key !== 'id' && key !== 'guildId' && key !== 'createdAt') {
                    fields.push(`${key} = ?`);
                    values.push(data[key]);
                }
            });

            if (fields.length === 0) {
                return this.findServerConfig(guildId).then(resolve).catch(reject);
            }

            fields.push('updatedAt = ?');
            values.push(new Date().toISOString());
            values.push(guildId);

            const sql = `UPDATE serverConfigs SET ${fields.join(', ')} WHERE guildId = ?`;

            this.db.run(sql, values, (err) => {
                if (err) {
                    reject(err);
                } else {
                    this.findServerConfig(guildId).then(resolve).catch(reject);
                }
            });
        });
    }

    /**
     * Close database connection
     */
    async close() {
        return new Promise((resolve) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        console.error('❌ Error closing database:', err);
                    } else {
                        console.log('✅ Database connection closed');
                    }
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }

    /**
     * Execute raw SQL query
     * @param {string} sql - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise<Array>} - Query results
     */
    async query(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    /**
     * Add pending reward for a user
     * @param {string} discordId - Discord user ID
     * @param {string} minecraftName - Minecraft username
     * @param {string} guildId - Guild ID
     * @param {string} rewardTier - Reward tier (common, rare, epic, mythical)
     * @param {Array} rewardItems - Array of reward items
     * @returns {Promise<Object>} - Created pending reward
     */
    async addPendingReward(discordId, minecraftName, guildId, rewardTier, rewardItems) {
        return new Promise((resolve, reject) => {
            const itemsJson = JSON.stringify(rewardItems);

            this.db.run(
                `INSERT INTO pendingRewards (discordId, minecraftName, guildId, rewardTier, rewardItems)
                 VALUES (?, ?, ?, ?, ?)`,
                [discordId, minecraftName, guildId, rewardTier, itemsJson],
                function(err) {
                    if (err) {
                        reject(err);
                    } else {
                        resolve({
                            id: this.lastID,
                            discordId,
                            minecraftName,
                            guildId,
                            rewardTier,
                            rewardItems,
                            distributed: false,
                            createdAt: new Date().toISOString(),
                            distributedAt: null
                        });
                    }
                }
            );
        });
    }

    /**
     * Get pending rewards for a player
     * @param {string} minecraftName - Minecraft username
     * @param {string} guildId - Guild ID
     * @returns {Promise<Array>} - Array of pending rewards
     */
    async getPendingRewards(minecraftName, guildId) {
        return new Promise((resolve, reject) => {
            this.db.all(
                'SELECT * FROM pendingRewards WHERE minecraftName = ? AND guildId = ? AND distributed = FALSE ORDER BY createdAt ASC',
                [minecraftName, guildId],
                (err, rows) => {
                    if (err) {
                        reject(err);
                    } else {
                        // Parse reward items JSON
                        const rewards = rows.map(row => ({
                            ...row,
                            rewardItems: JSON.parse(row.rewardItems)
                        }));
                        resolve(rewards);
                    }
                }
            );
        });
    }

    /**
     * Mark pending reward as distributed
     * @param {number} rewardId - Reward ID
     * @returns {Promise<void>}
     */
    async markRewardDistributed(rewardId) {
        return new Promise((resolve, reject) => {
            this.db.run(
                'UPDATE pendingRewards SET distributed = TRUE, distributedAt = ? WHERE id = ?',
                [new Date().toISOString(), rewardId],
                (err) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve();
                    }
                }
            );
        });
    }
}

module.exports = new Database();
