const { EmbedBuilder } = require('discord.js');
const database = require('./database');
const minecraftUtils = require('./minecraftUtils');
const rewardDistributor = require('./rewardDistributor');

class ChatBridge {
    constructor() {
        this.client = null;
        this.bridgeChannels = new Map(); // guildId -> channelId
    }

    /**
     * Initialize the chat bridge
     * @param {Client} client - Discord client instance
     */
    async initialize(client) {
        this.client = client;
        
        // Load bridge channels from database
        try {
            const configs = await database.query(
                'SELECT guildId, chatBridgeChannel FROM serverConfigs WHERE chatBridgeChannel IS NOT NULL'
            );

            for (const config of configs) {
                this.bridgeChannels.set(config.guildId, config.chatBridgeChannel);
            }

            console.log(`✅ Chat bridge initialized for ${configs.length} servers`);
        } catch (error) {
            console.error('❌ Error initializing chat bridge:', error);
        }
    }

    /**
     * Handle Discord message for potential bridging
     * @param {Message} message - Discord message
     */
    async handleDiscordMessage(message) {
        try {
            // Skip if not in a bridge channel
            const bridgeChannelId = this.bridgeChannels.get(message.guild?.id);
            if (!bridgeChannelId || message.channel.id !== bridgeChannelId) {
                return;
            }

            // Skip bot messages
            if (message.author.bot) return;

            // Get user's linked Minecraft account
            const user = await database.findUser(message.author.id);
            
            if (!user || !user.minecraftName) {
                // Send ephemeral message asking to link account
                const embed = new EmbedBuilder()
                    .setColor('#ff6b35')
                    .setTitle('🔗 Account Not Linked')
                    .setDescription('You need to link your Minecraft account to use the chat bridge!')
                    .addFields({
                        name: 'How to Link',
                        value: 'Use `/linkmc <minecraft_name>` to link your account.\n\n**Note for Bedrock players:** Add an underscore before your name (e.g., `_YourName`)',
                        inline: false
                    })
                    .setFooter({ text: 'The Basement - Chat Bridge' });

                // Try to send as reply, fallback to DM
                try {
                    await message.reply({ embeds: [embed], allowedMentions: { repliedUser: false } });
                    
                    // Delete the original message and the reply after 10 seconds
                    setTimeout(async () => {
                        try {
                            await message.delete();
                        } catch (error) {
                            // Message might already be deleted
                        }
                    }, 10000);
                } catch (error) {
                    // Fallback to DM
                    try {
                        await message.author.send({ embeds: [embed] });
                    } catch (dmError) {
                        console.log(`Could not notify ${message.author.tag} about linking account`);
                    }
                }
                return;
            }

            // Format message for Minecraft
            const formattedMessage = this.formatDiscordMessageForMinecraft(message, user.minecraftName);
            
            // Here you would send the message to Minecraft server
            // This depends on your Minecraft server setup (RCON, plugin API, etc.)
            console.log(`💬 Discord → Minecraft: ${formattedMessage}`);
            
            // For now, we'll just log it. In a real implementation, you'd use:
            // - RCON to send a tellraw command
            // - A plugin API
            // - File-based communication
            // - WebSocket connection to a plugin

        } catch (error) {
            console.error('❌ Error handling Discord message for bridge:', error);
        }
    }

    /**
     * Handle Minecraft message for bridging to Discord
     * @param {string} guildId - Guild ID
     * @param {string} playerName - Minecraft player name
     * @param {string} message - Chat message
     */
    async handleMinecraftMessage(guildId, playerName, message) {
        try {
            const bridgeChannelId = this.bridgeChannels.get(guildId);
            if (!bridgeChannelId) return;

            const guild = this.client.guilds.cache.get(guildId);
            if (!guild) return;

            const channel = guild.channels.cache.get(bridgeChannelId);
            if (!channel) return;

            // Get player avatar
            const avatarUrl = await minecraftUtils.getPlayerAvatar(playerName, 32);
            
            // Check if player is linked to a Discord account
            const user = await database.query(
                'SELECT * FROM users WHERE minecraftName = ?',
                [playerName]
            ).then(rows => rows[0] || null);
            let discordMention = '';
            
            if (user) {
                try {
                    const discordUser = await this.client.users.fetch(user.discordId);
                    discordMention = ` (${discordUser.displayName || discordUser.username})`;
                } catch (error) {
                    // Discord user not found or error fetching
                }
            }

            // Create embed for Minecraft message
            const embed = new EmbedBuilder()
                .setColor('#4CAF50')
                .setAuthor({
                    name: `${minecraftUtils.formatUsernameForDisplay(playerName)}${discordMention}`,
                    iconURL: avatarUrl
                })
                .setDescription(message)
                .setTimestamp()
                .setFooter({ 
                    text: `Minecraft${minecraftUtils.isBedrockPlayer(playerName) ? ' (Bedrock)' : ' (Java)'}`,
                    iconURL: 'https://minecraft.wiki/images/2/2d/Plains_Grass_Block.png'
                });

            await channel.send({ embeds: [embed] });

        } catch (error) {
            console.error('❌ Error handling Minecraft message for bridge:', error);
        }
    }

    /**
     * Format Discord message for Minecraft
     * @param {Message} message - Discord message
     * @param {string} minecraftName - Sender's Minecraft name
     * @returns {string} - Formatted message
     */
    formatDiscordMessageForMinecraft(message, minecraftName) {
        let content = message.content;

        // Handle mentions
        content = content.replace(/<@!?(\d+)>/g, (match, userId) => {
            const user = message.guild.members.cache.get(userId);
            return user ? `@${user.displayName}` : '@unknown';
        });

        // Handle channel mentions
        content = content.replace(/<#(\d+)>/g, (match, channelId) => {
            const channel = message.guild.channels.cache.get(channelId);
            return channel ? `#${channel.name}` : '#unknown';
        });

        // Handle role mentions
        content = content.replace(/<@&(\d+)>/g, (match, roleId) => {
            const role = message.guild.roles.cache.get(roleId);
            return role ? `@${role.name}` : '@unknown';
        });

        // Handle custom emojis
        content = content.replace(/<a?:\w+:\d+>/g, ':emoji:');

        // Handle attachments
        if (message.attachments.size > 0) {
            const attachmentTypes = Array.from(message.attachments.values())
                .map(att => this.getAttachmentType(att.name))
                .filter(type => type);
            
            if (attachmentTypes.length > 0) {
                content += ` [${attachmentTypes.join(', ')}]`;
            }
        }

        // Format final message
        const displayName = minecraftUtils.formatUsernameForDisplay(minecraftName);
        return `[Discord] <${displayName}> ${content}`;
    }

    /**
     * Get attachment type for display
     * @param {string} filename - Attachment filename
     * @returns {string} - Attachment type
     */
    getAttachmentType(filename) {
        const extension = filename.split('.').pop().toLowerCase();
        
        const imageTypes = ['png', 'jpg', 'jpeg', 'gif', 'webp', 'bmp'];
        const videoTypes = ['mp4', 'webm', 'mov', 'avi', 'mkv'];
        const audioTypes = ['mp3', 'wav', 'ogg', 'flac', 'm4a'];
        
        if (imageTypes.includes(extension)) return 'Image';
        if (videoTypes.includes(extension)) return 'Video';
        if (audioTypes.includes(extension)) return 'Audio';
        
        return 'File';
    }

    /**
     * Set bridge channel for a guild
     * @param {string} guildId - Guild ID
     * @param {string} channelId - Channel ID
     */
    setBridgeChannel(guildId, channelId) {
        this.bridgeChannels.set(guildId, channelId);
    }

    /**
     * Remove bridge channel for a guild
     * @param {string} guildId - Guild ID
     */
    removeBridgeChannel(guildId) {
        this.bridgeChannels.delete(guildId);
    }

    /**
     * Get bridge channel for a guild
     * @param {string} guildId - Guild ID
     * @returns {string|null} - Channel ID or null
     */
    getBridgeChannel(guildId) {
        return this.bridgeChannels.get(guildId) || null;
    }

    /**
     * Handle player join event and distribute rewards
     * @param {string} guildId - Guild ID
     * @param {string} playerName - Player name who joined
     */
    async handlePlayerJoin(guildId, playerName) {
        try {
            console.log(`🎮 Player joined: ${playerName} in guild ${guildId}`);

            // Send join message to bridge channel
            await this.sendSystemMessage(
                guildId,
                `🎮 **${minecraftUtils.formatUsernameForDisplay(playerName)}** joined the game`,
                'join'
            );

            // Check for pending rewards and distribute them
            const distributed = await rewardDistributor.distributePendingRewards(playerName, guildId);

            if (distributed) {
                console.log(`✅ Rewards distributed to ${playerName}`);
            }

        } catch (error) {
            console.error(`❌ Error handling player join for ${playerName}:`, error);
        }
    }

    /**
     * Handle player leave event
     * @param {string} guildId - Guild ID
     * @param {string} playerName - Player name who left
     */
    async handlePlayerLeave(guildId, playerName) {
        try {
            console.log(`👋 Player left: ${playerName} in guild ${guildId}`);

            // Send leave message to bridge channel
            await this.sendSystemMessage(
                guildId,
                `👋 **${minecraftUtils.formatUsernameForDisplay(playerName)}** left the game`,
                'leave'
            );

        } catch (error) {
            console.error(`❌ Error handling player leave for ${playerName}:`, error);
        }
    }

    /**
     * Parse Minecraft server log for events
     * @param {string} guildId - Guild ID
     * @param {string} logLine - Server log line
     */
    async parseServerLog(guildId, logLine) {
        try {
            // Common Minecraft server log patterns
            const joinPattern = /(\w+) joined the game/;
            const leavePattern = /(\w+) left the game/;
            const chatPattern = /<(\w+)> (.+)/;
            const deathPattern = /(\w+) (was|died|fell|drowned|burned|suffocated)/;

            // Handle player join
            const joinMatch = logLine.match(joinPattern);
            if (joinMatch) {
                const playerName = joinMatch[1];
                await this.handlePlayerJoin(guildId, playerName);
                return;
            }

            // Handle player leave
            const leaveMatch = logLine.match(leavePattern);
            if (leaveMatch) {
                const playerName = leaveMatch[1];
                await this.handlePlayerLeave(guildId, playerName);
                return;
            }

            // Handle chat message
            const chatMatch = logLine.match(chatPattern);
            if (chatMatch) {
                const playerName = chatMatch[1];
                const message = chatMatch[2];
                await this.handleMinecraftMessage(guildId, playerName, message);
                return;
            }

            // Handle death message
            const deathMatch = logLine.match(deathPattern);
            if (deathMatch) {
                const playerName = deathMatch[1];
                await this.sendSystemMessage(
                    guildId,
                    `💀 **${minecraftUtils.formatUsernameForDisplay(playerName)}** ${logLine.split(' ').slice(1).join(' ')}`,
                    'death'
                );
                return;
            }

        } catch (error) {
            console.error('❌ Error parsing server log:', error);
        }
    }

    /**
     * Send system message to bridge channel
     * @param {string} guildId - Guild ID
     * @param {string} message - System message
     * @param {string} type - Message type (join, leave, death, etc.)
     */
    async sendSystemMessage(guildId, message, type = 'system') {
        try {
            const bridgeChannelId = this.bridgeChannels.get(guildId);
            if (!bridgeChannelId) return;

            const guild = this.client.guilds.cache.get(guildId);
            if (!guild) return;

            const channel = guild.channels.cache.get(bridgeChannelId);
            if (!channel) return;

            const colors = {
                join: '#4CAF50',
                leave: '#F44336',
                death: '#FF9800',
                achievement: '#FFD700',
                system: '#9E9E9E'
            };

            const embed = new EmbedBuilder()
                .setColor(colors[type] || colors.system)
                .setDescription(message)
                .setTimestamp()
                .setFooter({ text: 'Minecraft Server' });

            await channel.send({ embeds: [embed] });

        } catch (error) {
            console.error('❌ Error sending system message to bridge:', error);
        }
    }
}

module.exports = new ChatBridge();
