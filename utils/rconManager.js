const Rcon = require('rcon');
const database = require('./database');

class RconManager {
    constructor() {
        this.connections = new Map(); // guildId -> rcon connection
        this.config = new Map(); // guildId -> rcon config
        this.reconnectAttempts = new Map(); // guildId -> attempt count
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 5000; // 5 seconds
    }

    /**
     * Add RCON configuration for a guild
     * @param {string} guildId - Guild ID
     * @param {Object} config - RCON configuration
     */
    setRconConfig(guildId, config) {
        this.config.set(guildId, {
            host: config.host || 'localhost',
            port: config.port || 25575,
            password: config.password,
            timeout: config.timeout || 5000
        });
        console.log(`✅ RCON config set for guild ${guildId}`);
    }

    /**
     * Connect to RCON server for a guild
     * @param {string} guildId - Guild ID
     * @returns {Promise<boolean>} - Connection success
     */
    async connect(guildId) {
        const config = this.config.get(guildId);
        if (!config || !config.password) {
            console.error(`❌ No RCON config found for guild ${guildId}`);
            return false;
        }

        try {
            // Close existing connection if any
            await this.disconnect(guildId);

            const rcon = new Rcon(config.host, config.port, config.password, {
                tcp: true,
                challenge: false
            });

            // Set up event handlers
            rcon.on('auth', () => {
                console.log(`✅ RCON authenticated for guild ${guildId}`);
                this.reconnectAttempts.set(guildId, 0);
            });

            rcon.on('response', (str) => {
                console.log(`📡 RCON response for ${guildId}:`, str);
            });

            rcon.on('error', (err) => {
                console.error(`❌ RCON error for guild ${guildId}:`, err.message);
                this.handleConnectionError(guildId);
            });

            rcon.on('end', () => {
                console.log(`🔌 RCON connection ended for guild ${guildId}`);
                this.connections.delete(guildId);
            });

            // Connect with timeout
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Connection timeout'));
                }, config.timeout);

                rcon.connect();

                rcon.on('auth', () => {
                    clearTimeout(timeout);
                    resolve();
                });

                rcon.on('error', (err) => {
                    clearTimeout(timeout);
                    reject(err);
                });
            });

            this.connections.set(guildId, rcon);
            return true;

        } catch (error) {
            console.error(`❌ Failed to connect RCON for guild ${guildId}:`, error.message);
            this.handleConnectionError(guildId);
            return false;
        }
    }

    /**
     * Disconnect RCON for a guild
     * @param {string} guildId - Guild ID
     */
    async disconnect(guildId) {
        const rcon = this.connections.get(guildId);
        if (rcon) {
            try {
                rcon.disconnect();
            } catch (error) {
                console.error(`❌ Error disconnecting RCON for guild ${guildId}:`, error.message);
            }
            this.connections.delete(guildId);
        }
    }

    /**
     * Send command to Minecraft server
     * @param {string} guildId - Guild ID
     * @param {string} command - Minecraft command
     * @returns {Promise<string|null>} - Command response
     */
    async sendCommand(guildId, command) {
        const rcon = this.connections.get(guildId);
        if (!rcon) {
            console.error(`❌ No RCON connection for guild ${guildId}`);
            // Try to reconnect
            const connected = await this.connect(guildId);
            if (!connected) return null;
            return this.sendCommand(guildId, command);
        }

        try {
            return await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Command timeout'));
                }, 10000);

                rcon.send(command, (response) => {
                    clearTimeout(timeout);
                    resolve(response);
                });
            });
        } catch (error) {
            console.error(`❌ Error sending RCON command for guild ${guildId}:`, error.message);
            return null;
        }
    }

    /**
     * Give items to a player
     * @param {string} guildId - Guild ID
     * @param {string} playerName - Player name
     * @param {Array} items - Array of items to give
     * @returns {Promise<boolean>} - Success status
     */
    async giveItems(guildId, playerName, items) {
        try {
            const cleanPlayerName = playerName.startsWith('_') ? playerName.substring(1) : playerName;
            
            for (const item of items) {
                const command = `give ${cleanPlayerName} ${item.item} ${item.amount || 1}`;
                const response = await this.sendCommand(guildId, command);
                
                if (response === null) {
                    console.error(`❌ Failed to give ${item.item} to ${cleanPlayerName}`);
                    return false;
                }
                
                console.log(`✅ Gave ${item.amount || 1} ${item.item} to ${cleanPlayerName}`);
            }
            
            return true;
        } catch (error) {
            console.error(`❌ Error giving items to ${playerName}:`, error.message);
            return false;
        }
    }

    /**
     * Send message to player
     * @param {string} guildId - Guild ID
     * @param {string} playerName - Player name
     * @param {string} message - Message to send
     * @returns {Promise<boolean>} - Success status
     */
    async tellPlayer(guildId, playerName, message) {
        try {
            const cleanPlayerName = playerName.startsWith('_') ? playerName.substring(1) : playerName;
            const command = `tell ${cleanPlayerName} ${message}`;
            const response = await this.sendCommand(guildId, command);
            return response !== null;
        } catch (error) {
            console.error(`❌ Error sending message to ${playerName}:`, error.message);
            return false;
        }
    }

    /**
     * Broadcast message to all players
     * @param {string} guildId - Guild ID
     * @param {string} message - Message to broadcast
     * @returns {Promise<boolean>} - Success status
     */
    async broadcast(guildId, message) {
        try {
            const command = `say ${message}`;
            const response = await this.sendCommand(guildId, command);
            return response !== null;
        } catch (error) {
            console.error(`❌ Error broadcasting message:`, error.message);
            return false;
        }
    }

    /**
     * Handle connection errors and attempt reconnection
     * @param {string} guildId - Guild ID
     */
    async handleConnectionError(guildId) {
        const attempts = this.reconnectAttempts.get(guildId) || 0;
        
        if (attempts < this.maxReconnectAttempts) {
            this.reconnectAttempts.set(guildId, attempts + 1);
            console.log(`🔄 Attempting to reconnect RCON for guild ${guildId} (attempt ${attempts + 1}/${this.maxReconnectAttempts})`);
            
            setTimeout(async () => {
                await this.connect(guildId);
            }, this.reconnectDelay);
        } else {
            console.error(`❌ Max reconnection attempts reached for guild ${guildId}`);
            this.reconnectAttempts.delete(guildId);
        }
    }

    /**
     * Check if RCON is connected for a guild
     * @param {string} guildId - Guild ID
     * @returns {boolean} - Connection status
     */
    isConnected(guildId) {
        return this.connections.has(guildId);
    }

    /**
     * Get connection status for all guilds
     * @returns {Object} - Connection status map
     */
    getConnectionStatus() {
        const status = {};
        for (const [guildId, config] of this.config.entries()) {
            status[guildId] = {
                configured: true,
                connected: this.isConnected(guildId),
                host: config.host,
                port: config.port
            };
        }
        return status;
    }

    /**
     * Disconnect all RCON connections
     */
    async disconnectAll() {
        console.log('🔌 Disconnecting all RCON connections...');
        const disconnectPromises = [];
        
        for (const guildId of this.connections.keys()) {
            disconnectPromises.push(this.disconnect(guildId));
        }
        
        await Promise.all(disconnectPromises);
        console.log('✅ All RCON connections disconnected');
    }
}

module.exports = new RconManager();
