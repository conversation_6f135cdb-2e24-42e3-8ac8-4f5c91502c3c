const database = require('./database');
const rconManager = require('./rconManager');
const { EmbedBuilder } = require('discord.js');

class RewardDistributor {
    constructor() {
        this.client = null;
        this.rewardTiers = {
            common: {
                color: '#9E9E9E',
                emoji: '📦',
                items: [
                    { item: 'minecraft:iron_ingot', amount: 16, name: 'Iron Ingots' },
                    { item: 'minecraft:coal', amount: 32, name: 'Coal' },
                    { item: 'minecraft:bread', amount: 16, name: 'Bread' },
                    { item: 'minecraft:arrow', amount: 64, name: 'Arrows' },
                    { item: 'minecraft:oak_log', amount: 32, name: 'Oak Logs' }
                ]
            },
            rare: {
                color: '#2196F3',
                emoji: '💎',
                items: [
                    { item: 'minecraft:diamond', amount: 4, name: 'Diamonds' },
                    { item: 'minecraft:gold_ingot', amount: 16, name: 'Gold Ingots' },
                    { item: 'minecraft:enchanted_book', amount: 1, name: 'Enchanted Book' },
                    { item: 'minecraft:ender_pearl', amount: 8, name: 'Ender Pearls' },
                    { item: 'minecraft:blaze_rod', amount: 4, name: '<PERSON> Rods' }
                ]
            },
            epic: {
                color: '#9C27B0',
                emoji: '🔮',
                items: [
                    { item: 'minecraft:netherite_ingot', amount: 1, name: 'Netherite Ingot' },
                    { item: 'minecraft:diamond_sword', amount: 1, name: 'Diamond Sword', enchanted: true },
                    { item: 'minecraft:diamond_pickaxe', amount: 1, name: 'Diamond Pickaxe', enchanted: true },
                    { item: 'minecraft:elytra', amount: 1, name: 'Elytra' },
                    { item: 'minecraft:totem_of_undying', amount: 1, name: 'Totem of Undying' }
                ]
            },
            mythical: {
                color: '#FFD700',
                emoji: '👑',
                items: [
                    { item: 'minecraft:netherite_sword', amount: 1, name: 'Netherite Sword', enchanted: true },
                    { item: 'minecraft:netherite_pickaxe', amount: 1, name: 'Netherite Pickaxe', enchanted: true },
                    { item: 'minecraft:beacon', amount: 1, name: 'Beacon' },
                    { item: 'minecraft:dragon_egg', amount: 1, name: 'Dragon Egg' },
                    { item: 'minecraft:nether_star', amount: 3, name: 'Nether Stars' }
                ]
            }
        };
    }

    /**
     * Initialize the reward distributor
     * @param {Client} client - Discord client instance
     */
    initialize(client) {
        this.client = client;
        console.log('✅ Reward distributor initialized');
    }

    /**
     * Generate random reward for a tier
     * @param {string} tier - Reward tier
     * @returns {Object} - Reward object
     */
    generateReward(tier) {
        const tierData = this.rewardTiers[tier];
        if (!tierData) {
            throw new Error(`Invalid reward tier: ${tier}`);
        }

        const randomItem = tierData.items[Math.floor(Math.random() * tierData.items.length)];
        
        return {
            tier,
            item: randomItem,
            color: tierData.color,
            emoji: tierData.emoji
        };
    }

    /**
     * Add pending reward for a user
     * @param {string} discordId - Discord user ID
     * @param {string} minecraftName - Minecraft username
     * @param {string} guildId - Guild ID
     * @param {string} tier - Reward tier
     * @returns {Promise<Object>} - Reward object
     */
    async addPendingReward(discordId, minecraftName, guildId, tier) {
        try {
            const reward = this.generateReward(tier);
            
            await database.addPendingReward(
                discordId,
                minecraftName,
                guildId,
                tier,
                [reward.item]
            );

            console.log(`✅ Added pending ${tier} reward for ${minecraftName}: ${reward.item.name}`);
            return reward;
        } catch (error) {
            console.error('❌ Error adding pending reward:', error);
            throw error;
        }
    }

    /**
     * Distribute pending rewards to a player
     * @param {string} minecraftName - Minecraft username
     * @param {string} guildId - Guild ID
     * @returns {Promise<boolean>} - Success status
     */
    async distributePendingRewards(minecraftName, guildId) {
        try {
            const pendingRewards = await database.getPendingRewards(minecraftName, guildId);
            
            if (pendingRewards.length === 0) {
                return true; // No rewards to distribute
            }

            console.log(`🎁 Distributing ${pendingRewards.length} pending rewards to ${minecraftName}`);

            // Group rewards by tier for better messaging
            const rewardsByTier = {};
            const allItems = [];

            for (const reward of pendingRewards) {
                if (!rewardsByTier[reward.rewardTier]) {
                    rewardsByTier[reward.rewardTier] = [];
                }
                rewardsByTier[reward.rewardTier].push(reward);
                allItems.push(...reward.rewardItems);
            }

            // Give items via RCON
            const success = await rconManager.giveItems(guildId, minecraftName, allItems);
            
            if (success) {
                // Send welcome message to player
                const tierCounts = Object.keys(rewardsByTier).map(tier => {
                    const count = rewardsByTier[tier].length;
                    const emoji = this.rewardTiers[tier].emoji;
                    return `${emoji} ${count} ${tier}`;
                }).join(', ');

                await rconManager.tellPlayer(
                    guildId,
                    minecraftName,
                    `§6§l[REWARDS] §r§eWelcome back! You received: ${tierCounts} rewards!`
                );

                // Mark all rewards as distributed
                for (const reward of pendingRewards) {
                    await database.markRewardDistributed(reward.id);
                }

                // Send notification to reward log channel
                await this.sendRewardLogNotification(guildId, minecraftName, rewardsByTier);

                console.log(`✅ Successfully distributed ${pendingRewards.length} rewards to ${minecraftName}`);
                return true;
            } else {
                console.error(`❌ Failed to distribute rewards to ${minecraftName} via RCON`);
                return false;
            }

        } catch (error) {
            console.error(`❌ Error distributing rewards to ${minecraftName}:`, error);
            return false;
        }
    }

    /**
     * Send reward distribution notification to log channel
     * @param {string} guildId - Guild ID
     * @param {string} minecraftName - Minecraft username
     * @param {Object} rewardsByTier - Rewards grouped by tier
     */
    async sendRewardLogNotification(guildId, minecraftName, rewardsByTier) {
        try {
            const serverConfig = await database.findServerConfig(guildId);
            if (!serverConfig || !serverConfig.rewardLogChannel) {
                return; // No reward log channel configured
            }

            const guild = this.client.guilds.cache.get(guildId);
            if (!guild) return;

            const channel = guild.channels.cache.get(serverConfig.rewardLogChannel);
            if (!channel) return;

            // Create embed for reward distribution
            const embed = new EmbedBuilder()
                .setColor('#4CAF50')
                .setTitle('🎁 Rewards Distributed')
                .setDescription(`Pending rewards have been automatically distributed to **${minecraftName}**`)
                .setTimestamp()
                .setFooter({ text: 'The Basement - Reward Distribution' });

            // Add fields for each tier
            for (const [tier, rewards] of Object.entries(rewardsByTier)) {
                const tierData = this.rewardTiers[tier];
                const itemList = rewards.map(reward => {
                    const items = reward.rewardItems.map(item => 
                        `${item.amount || 1}x ${item.name || item.item}`
                    ).join(', ');
                    return items;
                }).join('\n');

                embed.addFields({
                    name: `${tierData.emoji} ${tier.charAt(0).toUpperCase() + tier.slice(1)} (${rewards.length})`,
                    value: itemList || 'No items',
                    inline: true
                });
            }

            await channel.send({ embeds: [embed] });

        } catch (error) {
            console.error('❌ Error sending reward log notification:', error);
        }
    }

    /**
     * Get reward tier probabilities
     * @returns {Object} - Tier probabilities
     */
    getTierProbabilities() {
        return {
            common: 60,
            rare: 25,
            epic: 12,
            mythical: 3
        };
    }

    /**
     * Roll for reward tier based on probabilities
     * @returns {string} - Selected tier
     */
    rollRewardTier() {
        const probabilities = this.getTierProbabilities();
        const random = Math.random() * 100;
        
        let cumulative = 0;
        for (const [tier, probability] of Object.entries(probabilities)) {
            cumulative += probability;
            if (random <= cumulative) {
                return tier;
            }
        }
        
        return 'common'; // Fallback
    }

    /**
     * Get all available reward tiers
     * @returns {Array} - Array of tier names
     */
    getAvailableTiers() {
        return Object.keys(this.rewardTiers);
    }

    /**
     * Get tier information
     * @param {string} tier - Tier name
     * @returns {Object} - Tier information
     */
    getTierInfo(tier) {
        return this.rewardTiers[tier] || null;
    }
}

module.exports = new RewardDistributor();
