const { EmbedBuilder } = require('discord.js');
const database = require('./database');

class InviteTracker {
    constructor() {
        this.client = null;
        this.inviteCache = new Map();
    }

    /**
     * Initialize the invite tracker
     * @param {Client} client - Discord client instance
     */
    async initialize(client) {
        this.client = client;
        
        // Cache all existing invites for all guilds
        for (const guild of client.guilds.cache.values()) {
            await this.cacheGuildInvites(guild);
        }

        console.log('✅ Invite tracker initialized with cached invites');
    }

    /**
     * Cache invites for a specific guild
     * @param {Guild} guild - Discord guild
     */
    async cacheGuildInvites(guild) {
        try {
            const invites = await guild.invites.fetch();
            const guildInvites = new Map();

            for (const invite of invites.values()) {
                guildInvites.set(invite.code, {
                    uses: invite.uses,
                    inviterId: invite.inviterId,
                    maxUses: invite.maxUses
                });

                // Store in database (simplified for SQLite)
                // await this.updateInviteInDatabase(guild.id, invite);
            }

            this.inviteCache.set(guild.id, guildInvites);
            console.log(`📋 Cached ${invites.size} invites for guild ${guild.name}`);
        } catch (error) {
            console.error(`❌ Error caching invites for guild ${guild.name}:`, error);
        }
    }

    /**
     * Update invite information in database
     * @param {string} guildId - Guild ID
     * @param {Invite} invite - Discord invite object
     */
    async updateInviteInDatabase(guildId, invite) {
        try {
            // For now, we'll just use the cache for invite tracking
            // Full database invite tracking can be implemented later if needed
            console.log(`📝 Invite updated: ${invite.code} in ${guildId}`);
        } catch (error) {
            console.error('❌ Error updating invite in database:', error);
        }
    }

    /**
     * Handle member join event
     * @param {GuildMember} member - The member who joined
     */
    async handleMemberJoin(member) {
        try {
            const guild = member.guild;
            
            // Fetch current invites
            const currentInvites = await guild.invites.fetch();
            const cachedInvites = this.inviteCache.get(guild.id) || new Map();

            let usedInvite = null;

            // Find which invite was used
            for (const invite of currentInvites.values()) {
                const cached = cachedInvites.get(invite.code);
                
                if (cached && invite.uses > cached.uses) {
                    usedInvite = invite;
                    break;
                }
            }

            if (usedInvite) {
                await this.processInviteReward(member, usedInvite);
            }

            // Update cache with new invite data
            await this.cacheGuildInvites(guild);

        } catch (error) {
            console.error('❌ Error handling member join:', error);
        }
    }

    /**
     * Process invite reward for the inviter
     * @param {GuildMember} newMember - The new member
     * @param {Invite} invite - The invite that was used
     */
    async processInviteReward(newMember, invite) {
        try {
            if (!invite.inviterId) return;

            // Find or create inviter user record
            let inviter = await database.findOrCreateUser(invite.inviterId);

            // Award 10 spins to the inviter
            inviter = await database.updateUser(invite.inviterId, {
                spins: inviter.spins + 10,
                inviteCount: inviter.inviteCount + 1
            });

            // Update new member's record with who invited them
            await database.findOrCreateUser(newMember.id);
            await database.updateUser(newMember.id, {
                invitedBy: invite.inviterId
            });

            // Send reward notification
            await this.sendInviteRewardNotification(newMember, invite, inviter);

            console.log(`🎁 Invite reward: ${invite.inviterId} received 10 spins for inviting ${newMember.user.tag}`);

        } catch (error) {
            console.error('❌ Error processing invite reward:', error);
        }
    }

    /**
     * Send invite reward notification embed
     * @param {GuildMember} newMember - The new member
     * @param {Invite} invite - The invite that was used
     * @param {Object} inviterUser - The inviter's user record
     */
    async sendInviteRewardNotification(newMember, invite, inviterUser) {
        try {
            const guild = newMember.guild;
            const config = await database.findServerConfig(guild.id);

            if (!config || !config.rewardLogChannel) {
                console.log('⚠️ No reward log channel configured for invite notifications');
                return;
            }

            const channel = guild.channels.cache.get(config.rewardLogChannel);
            if (!channel) {
                console.log('⚠️ Reward log channel not found');
                return;
            }

            // Get inviter member
            const inviterMember = await guild.members.fetch(invite.inviterId).catch(() => null);
            
            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle('🎉 Invite Reward!')
                .setDescription(`**${inviterMember ? inviterMember.displayName : 'Unknown User'}** has been rewarded for inviting a new member!`)
                .addFields(
                    {
                        name: '👤 Inviter',
                        value: inviterMember ? `${inviterMember.displayName} (${inviterMember.user.tag})` : 'Unknown User',
                        inline: true
                    },
                    {
                        name: '🆕 New Member',
                        value: `${newMember.displayName} (${newMember.user.tag})`,
                        inline: true
                    },
                    {
                        name: '🎁 Reward',
                        value: '+10 Spins',
                        inline: true
                    },
                    {
                        name: '📊 Inviter Stats',
                        value: [
                            `Total Spins: ${inviterUser.spins}`,
                            `Total Invites: ${inviterUser.inviteCount}`
                        ].join('\n'),
                        inline: false
                    }
                )
                .setThumbnail(newMember.user.displayAvatarURL())
                .setTimestamp()
                .setFooter({ text: 'The Basement - Invite Tracker' });

            await channel.send({ embeds: [embed] });

            // Try to DM the inviter
            if (inviterMember) {
                try {
                    const dmEmbed = new EmbedBuilder()
                        .setColor('#00ff00')
                        .setTitle('🎉 You Earned Invite Rewards!')
                        .setDescription(`Thanks for inviting **${newMember.displayName}** to the server!`)
                        .addFields(
                            {
                                name: '🎁 Reward',
                                value: '+10 Spins',
                                inline: true
                            },
                            {
                                name: '🎰 Total Spins',
                                value: `${inviterUser.spins}`,
                                inline: true
                            }
                        )
                        .setFooter({ text: 'Use /spin to use your spins!' });

                    await inviterMember.send({ embeds: [dmEmbed] });
                } catch (dmError) {
                    // User has DMs disabled, that's okay
                    console.log(`📝 Could not send DM to inviter ${inviterMember.user.tag}`);
                }
            }

        } catch (error) {
            console.error('❌ Error sending invite reward notification:', error);
        }
    }

    /**
     * Get invite statistics for a user
     * @param {string} userId - Discord user ID
     * @returns {Promise<Object>} - Invite statistics
     */
    async getUserInviteStats(userId) {
        try {
            const user = await database.findUser(userId);
            
            if (!user) {
                return {
                    inviteCount: 0,
                    spinsEarned: 0,
                    totalSpins: 0
                };
            }

            return {
                inviteCount: user.inviteCount || 0,
                spinsEarned: (user.inviteCount || 0) * 10,
                totalSpins: user.spins || 0
            };
        } catch (error) {
            console.error('❌ Error getting user invite stats:', error);
            return null;
        }
    }

    /**
     * Get top inviters for a guild
     * @param {string} guildId - Guild ID
     * @param {number} limit - Number of top inviters to return
     * @returns {Promise<Array>} - Array of top inviters
     */
    async getTopInviters(guildId, limit = 10) {
        try {
            const topInviters = await database.query(
                'SELECT * FROM users WHERE inviteCount > 0 ORDER BY inviteCount DESC LIMIT ?',
                [limit]
            );

            return topInviters;
        } catch (error) {
            console.error('❌ Error getting top inviters:', error);
            return [];
        }
    }

    /**
     * Refresh invite cache for a guild
     * @param {string} guildId - Guild ID
     */
    async refreshGuildInvites(guildId) {
        try {
            const guild = this.client.guilds.cache.get(guildId);
            if (guild) {
                await this.cacheGuildInvites(guild);
                console.log(`🔄 Refreshed invite cache for guild ${guild.name}`);
            }
        } catch (error) {
            console.error('❌ Error refreshing guild invites:', error);
        }
    }
}

module.exports = new InviteTracker();
