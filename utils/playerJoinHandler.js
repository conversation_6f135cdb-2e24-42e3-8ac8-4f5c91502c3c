const database = require('./database');
const rewardDistributor = require('./rewardDistributor');
const { EmbedBuilder } = require('discord.js');

class PlayerJoinHandler {
    constructor() {
        this.client = null;
    }

    /**
     * Initialize the player join handler
     * @param {Client} client - Discord client instance
     */
    initialize(client) {
        this.client = client;
        console.log('✅ Player join handler initialized');
    }

    /**
     * Handle player join event and distribute rewards
     * @param {string} guildId - Guild ID
     * @param {string} playerName - Player name who joined
     * @returns {Promise<boolean>} - Success status
     */
    async handlePlayerJoin(guildId, playerName) {
        try {
            console.log(`🎮 Player joined: ${playerName} in guild ${guildId}`);

            // Check for pending rewards and distribute them
            const distributed = await rewardDistributor.distributePendingRewards(playerName, guildId);
            
            if (distributed) {
                console.log(`✅ Rewards distributed to ${playerName}`);
                
                // Send notification to reward log channel if configured
                await this.sendJoinNotification(guildId, playerName);
                
                return true;
            }

            return false;

        } catch (error) {
            console.error(`❌ Error handling player join for ${playerName}:`, error);
            return false;
        }
    }

    /**
     * Send player join notification to reward log channel
     * @param {string} guildId - Guild ID
     * @param {string} playerName - Player name
     */
    async sendJoinNotification(guildId, playerName) {
        try {
            const serverConfig = await database.findServerConfig(guildId);
            if (!serverConfig || !serverConfig.rewardLogChannel) {
                return; // No reward log channel configured
            }

            const guild = this.client.guilds.cache.get(guildId);
            if (!guild) return;

            const channel = guild.channels.cache.get(serverConfig.rewardLogChannel);
            if (!channel) return;

            // Check if player had pending rewards
            const pendingRewards = await database.query(
                'SELECT COUNT(*) as count FROM pendingRewards WHERE minecraftName = ? AND guildId = ? AND distributed = TRUE AND distributedAt > datetime("now", "-1 minute")',
                [playerName, guildId]
            );

            const rewardCount = pendingRewards[0]?.count || 0;

            if (rewardCount > 0) {
                const embed = new EmbedBuilder()
                    .setColor('#4CAF50')
                    .setTitle('🎮 Player Joined & Rewards Distributed')
                    .setDescription(`**${playerName}** joined the server and received **${rewardCount}** pending rewards!`)
                    .setTimestamp()
                    .setFooter({ text: 'The Basement - Player Join' });

                await channel.send({ embeds: [embed] });
            }

        } catch (error) {
            console.error('❌ Error sending join notification:', error);
        }
    }

    /**
     * Handle multiple player joins (batch processing)
     * @param {string} guildId - Guild ID
     * @param {Array<string>} playerNames - Array of player names
     * @returns {Promise<Object>} - Results summary
     */
    async handleMultiplePlayerJoins(guildId, playerNames) {
        const results = {
            successful: [],
            failed: [],
            totalRewards: 0
        };

        for (const playerName of playerNames) {
            try {
                const success = await this.handlePlayerJoin(guildId, playerName);
                if (success) {
                    results.successful.push(playerName);
                } else {
                    results.failed.push(playerName);
                }
            } catch (error) {
                console.error(`❌ Error processing join for ${playerName}:`, error);
                results.failed.push(playerName);
            }
        }

        return results;
    }

    /**
     * Get pending rewards count for a player
     * @param {string} playerName - Player name
     * @param {string} guildId - Guild ID
     * @returns {Promise<number>} - Number of pending rewards
     */
    async getPendingRewardsCount(playerName, guildId) {
        try {
            const pendingRewards = await database.getPendingRewards(playerName, guildId);
            return pendingRewards.length;
        } catch (error) {
            console.error(`❌ Error getting pending rewards count for ${playerName}:`, error);
            return 0;
        }
    }

    /**
     * Check if player has any pending rewards
     * @param {string} playerName - Player name
     * @param {string} guildId - Guild ID
     * @returns {Promise<boolean>} - Whether player has pending rewards
     */
    async hasPendingRewards(playerName, guildId) {
        const count = await this.getPendingRewardsCount(playerName, guildId);
        return count > 0;
    }
}

module.exports = new PlayerJoinHandler();
