require('dotenv').config();
const { Client, GatewayIntentBits, Collection, Events } = require('discord.js');
const fs = require('fs');
const path = require('path');

// Import utilities
const database = require('./utils/database');
const commandHandler = require('./utils/commandHandler');
const inviteTracker = require('./utils/inviteTracker');
const minecraftUtils = require('./utils/minecraftUtils');
const rewardDistributor = require('./utils/rewardDistributor');
const rconManager = require('./utils/rconManager');
const playerJoinHandler = require('./utils/playerJoinHandler');

// Create Discord client with necessary intents
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildInvites,
        GatewayIntentBits.GuildMessageReactions
    ]
});

// Initialize commands collection
client.commands = new Collection();
client.invites = new Map();

// Load commands
const commandsPath = path.join(__dirname, 'commands');
if (fs.existsSync(commandsPath)) {
    const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));
    
    for (const file of commandFiles) {
        const filePath = path.join(commandsPath, file);
        const command = require(filePath);
        
        if ('data' in command && 'execute' in command) {
            client.commands.set(command.data.name, command);
            console.log(`✅ Loaded command: ${command.data.name}`);
        } else {
            console.log(`⚠️  Command at ${filePath} is missing required "data" or "execute" property.`);
        }
    }
}

// Bot ready event
client.once(Events.ClientReady, async (readyClient) => {
    console.log(`🚀 The Basement Bot is ready! Logged in as ${readyClient.user.tag}`);

    // Register slash commands
    try {
        await commandHandler.registerCommands(client);
        console.log('✅ Slash commands registered successfully');
    } catch (error) {
        console.error('❌ Error registering slash commands:', error);
    }

    // Initialize invite tracking
    try {
        await inviteTracker.initialize(client);
        console.log('✅ Invite tracker initialized');
    } catch (error) {
        console.error('❌ Error initializing invite tracker:', error);
    }



    // Initialize reward distributor
    try {
        rewardDistributor.initialize(client);
        console.log('✅ Reward distributor initialized');
    } catch (error) {
        console.error('❌ Error initializing reward distributor:', error);
    }

    // Initialize player join handler
    try {
        playerJoinHandler.initialize(client);
        console.log('✅ Player join handler initialized');
    } catch (error) {
        console.error('❌ Error initializing player join handler:', error);
    }

    // Set bot status
    client.user.setActivity('The Basement | /linkmc | discord.gg/GCNHctBhk9', { type: 'WATCHING' });
});

// Handle slash command interactions
client.on(Events.InteractionCreate, async (interaction) => {
    if (interaction.isChatInputCommand()) {
        const command = client.commands.get(interaction.commandName);

        if (!command) {
            console.error(`❌ No command matching ${interaction.commandName} was found.`);
            return;
        }

        try {
            await command.execute(interaction);
        } catch (error) {
            console.error(`❌ Error executing command ${interaction.commandName}:`, error);

            const errorMessage = {
                content: '❌ There was an error while executing this command!',
                ephemeral: true
            };

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp(errorMessage);
            } else {
                await interaction.reply(errorMessage);
            }
        }
    } else if (interaction.isButton()) {
        // Handle button interactions
        try {
            if (interaction.customId.startsWith('link_')) {
                // Handle linkmc button interactions
                const linkmcCommand = client.commands.get('linkmc');
                if (linkmcCommand && linkmcCommand.handleButtonInteraction) {
                    await linkmcCommand.handleButtonInteraction(interaction);
                }
            } else if (interaction.customId.startsWith('start_linking_')) {
                // Handle spin command linking button
                const userId = interaction.customId.split('_')[2];
                if (userId !== interaction.user.id) {
                    return await interaction.reply({
                        content: '❌ You can only link your own account!',
                        ephemeral: true
                    });
                }

                // Show the linkmc interface
                const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

                const linkEmbed = new EmbedBuilder()
                    .setColor('#4CAF50')
                    .setTitle('🎮 Link Your Minecraft Account')
                    .setDescription('Please enter your Minecraft username (without any prefixes) and select your platform:')
                    .addFields(
                        {
                            name: '📋 Username Rules',
                            value: [
                                '• 3-16 characters long',
                                '• Letters, numbers, and underscores only',
                                '• Do not include any prefixes (like `_`)'
                            ].join('\n'),
                            inline: false
                        },
                        {
                            name: '💡 Examples',
                            value: '`Steve`, `Notch`, `Player123`, `CoolGamer_2024`',
                            inline: false
                        }
                    )
                    .setFooter({ text: 'Use /linkmc <minecraft_name> to link your account' });

                await interaction.update({ embeds: [linkEmbed], components: [] });
            }
            // Add more button handlers here as needed
        } catch (error) {
            console.error('❌ Error handling button interaction:', error);

            const errorMessage = {
                content: '❌ There was an error while processing this interaction!',
                ephemeral: true
            };

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp(errorMessage);
            } else {
                await interaction.reply(errorMessage);
            }
        }
    }
});

// Handle member join for invite tracking
client.on(Events.GuildMemberAdd, async (member) => {
    try {
        await inviteTracker.handleMemberJoin(member);
    } catch (error) {
        console.error('❌ Error handling member join:', error);
    }
});



// Handle reaction add for reaction roles
client.on(Events.MessageReactionAdd, async (reaction, user) => {
    if (user.bot) return;

    try {
        // Handle reaction role logic here
        // This will be implemented in the reaction role handler
    } catch (error) {
        console.error('❌ Error handling reaction add:', error);
    }
});

// Handle reaction remove for reaction roles
client.on(Events.MessageReactionRemove, async (reaction, user) => {
    if (user.bot) return;

    try {
        // Handle reaction role logic here
        // This will be implemented in the reaction role handler
    } catch (error) {
        console.error('❌ Error handling reaction remove:', error);
    }
});

// Error handling
client.on(Events.Error, (error) => {
    console.error('❌ Discord client error:', error);
});

process.on('unhandledRejection', (error) => {
    console.error('❌ Unhandled promise rejection:', error);
});

process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught exception:', error);
    process.exit(1);
});

// Initialize database
async function initializeDatabase() {
    try {
        await database.initialize();
        console.log('✅ Database initialized successfully');
    } catch (error) {
        console.error('❌ Database initialization error:', error);
        process.exit(1);
    }
}

// No monthly rewards in this version - removed cron scheduling

// Initialize bot
async function startBot() {
    try {
        await initializeDatabase();
        await client.login(process.env.BOT_TOKEN);
    } catch (error) {
        console.error('❌ Error starting bot:', error);
        process.exit(1);
    }
}

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('🛑 Shutting down bot...');

    try {
        await database.close();
        console.log('✅ Database connection closed');

        await rconManager.disconnectAll();
        console.log('✅ RCON connections closed');

        client.destroy();
        console.log('✅ Discord client destroyed');

        process.exit(0);
    } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
    }
});

// Start the bot
startBot();
